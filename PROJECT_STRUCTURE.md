# 📁 Структура проекта Grid Trading Bot

## 🏗️ Общая архитектура

```
trading-service/
├── 📄 README.md                    # Основная документация
├── 📄 QUICKSTART.md               # Быстрый старт
├── 📄 PROJECT_STRUCTURE.md        # Этот файл
├── 📄 implementation_progress.md   # Прогресс реализации
├── 📄 plan.md                     # Исходный план проекта
├── 📄 test_api.py                 # Скрипт тестирования API
├── 🐳 docker-compose.yml          # Docker конфигурация
│
├── 🔧 backend/                    # Backend приложение (FastAPI)
│   ├── 📄 Dockerfile
│   ├── 📄 requirements.txt        # Python зависимости
│   ├── 📄 run.py                  # Скрипт запуска
│   ├── 📄 create_migration.py     # Создание миграций БД
│   ├── 📄 alembic.ini            # Конфигурация Alembic
│   ├── 📄 .env.example           # Пример переменных окружения
│   │
│   ├── 📁 alembic/               # Миграции базы данных
│   │   ├── 📄 env.py
│   │   └── 📄 script.py.mako
│   │
│   └── 📁 app/                   # Основное приложение
│       ├── 📄 __init__.py
│       ├── 📄 main.py            # Точка входа FastAPI
│       │
│       ├── 📁 api/               # REST API
│       │   └── 📁 v1/
│       │       ├── 📄 api.py     # Главный роутер
│       │       └── 📁 endpoints/
│       │           ├── 📄 auth.py        # Аутентификация
│       │           ├── 📄 strategies.py  # Управление стратегиями
│       │           ├── 📄 trading.py     # Торговые операции
│       │           └── 📄 exchanges.py   # Работа с биржами
│       │
│       ├── 📁 core/              # Основная логика
│       │   ├── 📄 config.py      # Конфигурация приложения
│       │   ├── 📄 database.py    # Настройка БД
│       │   ├── 📄 trading_engine.py     # Торговый движок
│       │   ├── 📄 grid_algorithm.py     # Алгоритм сеточной торговли
│       │   ├── 📄 exchange_manager.py   # Менеджер бирж (CCXT)
│       │   └── 📄 risk_manager.py       # Управление рисками
│       │
│       ├── 📁 models/            # Модели базы данных
│       │   ├── 📄 __init__.py
│       │   ├── 📄 enums.py       # Перечисления
│       │   ├── 📄 user.py        # Модель пользователя
│       │   ├── 📄 strategy.py    # Модель стратегии
│       │   ├── 📄 order.py       # Модель ордера
│       │   └── 📄 trade.py       # Модель сделки
│       │
│       ├── 📁 schemas/           # Pydantic схемы
│       │   ├── 📄 __init__.py
│       │   └── 📄 strategy.py    # Схемы для стратегий
│       │
│       └── 📁 services/          # Бизнес-логика
│           ├── 📄 __init__.py
│           ├── 📄 strategy_service.py    # Сервис стратегий
│           └── 📄 telegram_service.py    # Telegram уведомления
│
└── 🌐 frontend/                  # Frontend приложение (React)
    ├── 📄 package.json          # Node.js зависимости
    ├── 📁 public/
    │   └── 📄 index.html
    └── 📁 src/
        ├── 📄 index.tsx         # Точка входа React
        └── 📄 App.tsx           # Главный компонент
```

## 🔧 Ключевые компоненты

### Backend (FastAPI)

#### 🎯 **TradingEngine** (`core/trading_engine.py`)
- Управление жизненным циклом стратегий
- Запуск/остановка/пауза стратегий
- Мониторинг состояния
- Восстановление после перезапуска

#### 🕸️ **GridAlgorithm** (`core/grid_algorithm.py`)
- Реализация алгоритма сеточной торговли
- Создание уровней сетки
- Размещение и мониторинг ордеров
- Обработка исполнений и перестройка сетки

#### 🏦 **ExchangeManager** (`core/exchange_manager.py`)
- Интеграция с CCXT библиотекой
- Поддержка Binance и Bybit
- Управление подключениями к биржам
- Унифицированный API для торговых операций

#### ⚠️ **RiskManager** (`core/risk_manager.py`)
- Валидация параметров стратегий
- Мониторинг рисков в реальном времени
- Лимиты на депозиты, плечо, просадку
- Оценка рисков стратегий

### База данных (PostgreSQL)

#### 📊 **Модели данных:**
- **User** - Пользователи системы
- **Strategy** - Торговые стратегии
- **Order** - Ордера на биржах
- **Trade** - Исполненные сделки

### API Endpoints

#### 📈 **Стратегии** (`/api/v1/strategies/`)
- `GET /` - Список стратегий
- `POST /` - Создание стратегии
- `GET /{id}` - Получение стратегии
- `PUT /{id}` - Обновление стратегии
- `DELETE /{id}` - Удаление стратегии
- `POST /{id}/start` - Запуск стратегии
- `POST /{id}/stop` - Остановка стратегии

#### 💰 **Торговля** (`/api/v1/trading/`)
- `GET /portfolio` - Портфель
- `GET /positions` - Позиции
- `GET /orders` - Ордера
- `GET /trades` - Сделки

#### 🏦 **Биржи** (`/api/v1/exchanges/`)
- `GET /` - Список бирж
- `POST /{name}/connect` - Подключение
- `GET /{name}/markets` - Рынки
- `GET /{name}/symbols` - Символы

## 🐳 Docker Services

### Основные сервисы:
- **postgres** - База данных PostgreSQL
- **redis** - Кэш и очереди задач
- **backend** - FastAPI приложение
- **frontend** - React приложение
- **celery_worker** - Фоновые задачи
- **celery_beat** - Периодические задачи

## 🔄 Потоки данных

### 1. Создание стратегии:
```
Frontend → API → StrategyService → Database
```

### 2. Запуск стратегии:
```
API → TradingEngine → GridAlgorithm → ExchangeManager → CCXT → Биржа
```

### 3. Мониторинг:
```
GridAlgorithm → ExchangeManager → Database → API → Frontend
```

### 4. Уведомления:
```
TradingEngine → TelegramService → Telegram Bot → Пользователь
```

## 🚀 Технологии

### Backend:
- **FastAPI** - Веб-фреймворк
- **SQLAlchemy** - ORM
- **Alembic** - Миграции БД
- **CCXT** - Интеграция с биржами
- **Celery** - Фоновые задачи
- **Redis** - Кэш и брокер сообщений
- **aiogram** - Telegram бот

### Frontend:
- **React** - UI библиотека
- **TypeScript** - Типизация
- **Material-UI** - Компоненты интерфейса

### Infrastructure:
- **PostgreSQL** - Основная БД
- **Docker** - Контейнеризация
- **Docker Compose** - Оркестрация

## 📝 Конфигурация

### Переменные окружения (`.env`):
```bash
# База данных
DATABASE_URL=postgresql://user:pass@localhost:5432/gridbot
REDIS_URL=redis://localhost:6379

# Безопасность
SECRET_KEY=your-secret-key
ALGORITHM=HS256

# Биржи
BINANCE_API_KEY=your-api-key
BINANCE_SECRET=your-secret
BINANCE_SANDBOX=true

# Telegram
TELEGRAM_BOT_TOKEN=your-bot-token
```

## 🎯 Готовность компонентов

✅ **Полностью готово:**
- Backend API с полным функционалом
- Торговый движок и алгоритм сетки
- Интеграция CCXT с биржами
- База данных с моделями
- Docker контейнеризация
- Базовый React frontend

🔄 **Требует доработки:**
- Полная интеграция Telegram
- Аутентификация пользователей
- Расширенный frontend
- Production конфигурация
