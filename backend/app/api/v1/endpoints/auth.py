"""
API эндпоинты для аутентификации
"""
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from fastapi import APIRouter, HTTPException, status, Depends, Request
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db
from app.models.user import User, UserRole
from app.schemas.user import (
    UserLogin, UserRegister, TokenResponse, UserResponse,
    EmailVerificationRequest, PasswordResetRequest, PasswordResetConfirm,
    OTPSetupResponse, OTPVerifyRequest, OTPDisableRequest, BackupCodesResponse
)
from app.services.user_service import UserService
from app.services.email_service import EmailService

security = HTTPBearer()
router = APIRouter()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: AsyncSession = Depends(get_db)
) -> User:
    """Получить текущего пользователя по токену"""
    user_service = UserService(db)
    user = await user_service.verify_token(credentials.credentials)

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Неверный токен",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """Получить текущего активного пользователя"""
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Неактивный пользователь"
        )
    return current_user


async def get_admin_user(current_user: User = Depends(get_current_active_user)) -> User:
    """Получить текущего пользователя с правами администратора"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Недостаточно прав"
        )
    return current_user


@router.post("/register", response_model=UserResponse)
async def register(
    user_data: UserRegister,
    db: AsyncSession = Depends(get_db)
):
    """Регистрация нового пользователя"""
    user_service = UserService(db)
    email_service = EmailService()

    try:
        user = await user_service.create_user(user_data)

        # Отправить email с подтверждением
        if user.email_verification_token:
            await email_service.send_verification_email(
                user.email,
                user.email_verification_token,
                user.username
            )

        return user
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка при регистрации пользователя"
        )


@router.post("/login", response_model=TokenResponse)
async def login(
    login_data: UserLogin,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """Вход в систему"""
    user_service = UserService(db)

    try:
        user = await user_service.authenticate_user(login_data)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Неверный логин или пароль",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Создать токен
        access_token = await user_service.create_access_token(user)

        # Отправить уведомление о входе (опционально)
        # client_ip = request.client.host
        # email_service = EmailService()
        # await email_service.send_login_notification(user.email, user.username, client_ip)

        return TokenResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=user_service.access_token_expire_minutes * 60,
            user=UserResponse.from_orm(user)
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка при входе в систему"
        )


@router.get("/me", response_model=UserResponse)
async def get_me(current_user: User = Depends(get_current_active_user)):
    """Получить информацию о текущем пользователе"""
    return current_user


@router.post("/verify-email")
async def verify_email(
    verification_data: EmailVerificationRequest,
    db: AsyncSession = Depends(get_db)
):
    """Подтвердить email адрес"""
    user_service = UserService(db)

    success = await user_service.verify_email(verification_data.token)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Неверный или истекший токен"
        )

    return {"message": "Email успешно подтвержден"}


@router.post("/request-password-reset")
async def request_password_reset(
    reset_data: PasswordResetRequest,
    db: AsyncSession = Depends(get_db)
):
    """Запросить сброс пароля"""
    user_service = UserService(db)
    email_service = EmailService()

    user = await user_service.get_user_by_email(reset_data.email)
    if user:
        token = user.generate_password_reset_token()
        await db.commit()

        await email_service.send_password_reset_email(
            user.email, token, user.username
        )

    # Всегда возвращаем успех для безопасности
    return {"message": "Если email существует, инструкции отправлены"}


@router.post("/logout")
async def logout():
    """Выход из системы"""
    return {"message": "Выход выполнен успешно"}
