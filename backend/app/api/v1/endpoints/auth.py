"""
API эндпоинты для аутентификации
"""
from fastapi import APIRouter

router = APIRouter()


@router.post("/login")
async def login():
    """Вход в систему"""
    # Временная заглушка
    return {"message": "Аутентификация будет реализована позже"}


@router.post("/register")
async def register():
    """Регистрация"""
    # Временная заглушка
    return {"message": "Регистрация будет реализована позже"}


@router.post("/logout")
async def logout():
    """Выход из системы"""
    # Временная заглушка
    return {"message": "Выход выполнен"}
