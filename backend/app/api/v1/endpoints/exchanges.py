"""
API эндпоинты для работы с биржами
"""
from typing import List
from fastapi import APIRouter, HTTPException, status
import structlog

from app.core.exchange_manager import exchange_manager

logger = structlog.get_logger()
router = APIRouter()


@router.get("/")
async def get_exchanges():
    """Получить список поддерживаемых бирж"""
    return {
        "supported_exchanges": ["binance", "bybit"],
        "connected_exchanges": list(exchange_manager.exchanges.keys())
    }


@router.post("/{exchange_name}/connect")
async def connect_exchange(exchange_name: str):
    """Подключиться к бирже"""
    try:
        exchange = await exchange_manager.connect_exchange(exchange_name)
        
        return {
            "message": f"Подключение к {exchange_name} успешно",
            "exchange": exchange_name,
            "markets_count": len(exchange.markets) if exchange.markets else 0
        }
        
    except Exception as e:
        logger.error(f"Ошибка подключения к {exchange_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Ошибка подключения к {exchange_name}: {str(e)}"
        )


@router.post("/{exchange_name}/disconnect")
async def disconnect_exchange(exchange_name: str):
    """Отключиться от биржи"""
    try:
        await exchange_manager.disconnect_exchange(exchange_name)
        
        return {
            "message": f"Отключение от {exchange_name} успешно",
            "exchange": exchange_name
        }
        
    except Exception as e:
        logger.error(f"Ошибка отключения от {exchange_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Ошибка отключения от {exchange_name}: {str(e)}"
        )


@router.get("/{exchange_name}/markets")
async def get_markets(exchange_name: str):
    """Получить рынки биржи"""
    try:
        exchange = await exchange_manager.get_exchange(exchange_name)
        
        # Фильтруем только фьючерсные рынки
        futures_markets = {}
        for symbol, market in exchange.markets.items():
            if market.get('type') in ['future', 'swap'] and market.get('active', True):
                futures_markets[symbol] = {
                    'symbol': symbol,
                    'base': market.get('base'),
                    'quote': market.get('quote'),
                    'settle': market.get('settle'),
                    'type': market.get('type'),
                    'contractSize': market.get('contractSize'),
                    'linear': market.get('linear'),
                    'inverse': market.get('inverse')
                }
        
        return {
            "exchange": exchange_name,
            "futures_markets": futures_markets,
            "total_count": len(futures_markets)
        }
        
    except Exception as e:
        logger.error(f"Ошибка получения рынков {exchange_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения рынков {exchange_name}: {str(e)}"
        )


@router.get("/{exchange_name}/symbols")
async def get_symbols(exchange_name: str):
    """Получить символы для торговли"""
    try:
        exchange = await exchange_manager.get_exchange(exchange_name)
        
        # Популярные фьючерсные пары
        popular_symbols = [
            'BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT',
            'ADA/USDT:USDT', 'SOL/USDT:USDT', 'DOT/USDT:USDT',
            'MATIC/USDT:USDT', 'AVAX/USDT:USDT', 'LINK/USDT:USDT'
        ]
        
        available_symbols = []
        for symbol in popular_symbols:
            if symbol in exchange.markets:
                market = exchange.markets[symbol]
                available_symbols.append({
                    'symbol': symbol,
                    'base': market.get('base'),
                    'quote': market.get('quote'),
                    'settle': market.get('settle'),
                    'active': market.get('active', True)
                })
        
        return {
            "exchange": exchange_name,
            "symbols": available_symbols
        }
        
    except Exception as e:
        logger.error(f"Ошибка получения символов {exchange_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения символов {exchange_name}: {str(e)}"
        )


@router.get("/{exchange_name}/status")
async def get_exchange_status(exchange_name: str):
    """Получить статус биржи"""
    try:
        is_connected = exchange_name in exchange_manager.exchanges
        
        if is_connected:
            exchange = exchange_manager.exchanges[exchange_name]
            # Проверить подключение
            try:
                await exchange.fetch_ticker('BTC/USDT:USDT')
                status_info = "connected"
            except:
                status_info = "connection_error"
        else:
            status_info = "disconnected"
        
        return {
            "exchange": exchange_name,
            "status": status_info,
            "is_connected": is_connected
        }
        
    except Exception as e:
        logger.error(f"Ошибка получения статуса {exchange_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения статуса {exchange_name}: {str(e)}"
        )
