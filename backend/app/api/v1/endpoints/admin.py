"""
API эндпоинты для администрирования.
"""

from typing import Optional
from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from app.database import get_async_db
from app.models.user import User, UserRole, UserStatus
from app.schemas.user import UserResponse, AdminUserUpdate, UserListResponse
from app.api.v1.endpoints.auth import get_admin_user

router = APIRouter()


@router.get("/users", response_model=UserListResponse)
async def get_users(
    page: int = Query(1, ge=1, description="Номер страницы"),
    per_page: int = Query(20, ge=1, le=100, description="Количество пользователей на странице"),
    search: Optional[str] = Query(None, description="Поиск по имени пользователя или email"),
    status_filter: Optional[UserStatus] = Query(None, description="Фильтр по статусу"),
    role_filter: Optional[UserRole] = Query(None, description="Фильтр по роли"),
    current_user: User = Depends(get_admin_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Получить список пользователей (только для админов)"""
    try:
        # Базовый запрос
        query = select(User)
        count_query = select(func.count(User.id))
        
        # Применяем фильтры
        if search:
            search_filter = (
                User.username.ilike(f"%{search}%") |
                User.email.ilike(f"%{search}%") |
                User.full_name.ilike(f"%{search}%")
            )
            query = query.where(search_filter)
            count_query = count_query.where(search_filter)
        
        if status_filter:
            query = query.where(User.status == status_filter)
            count_query = count_query.where(User.status == status_filter)
        
        if role_filter:
            query = query.where(User.role == role_filter)
            count_query = count_query.where(User.role == role_filter)
        
        # Получаем общее количество
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # Применяем пагинацию
        offset = (page - 1) * per_page
        query = query.offset(offset).limit(per_page).order_by(User.created_at.desc())
        
        # Выполняем запрос
        result = await db.execute(query)
        users = result.scalars().all()
        
        # Вычисляем количество страниц
        pages = (total + per_page - 1) // per_page
        
        return UserListResponse(
            users=[UserResponse.from_orm(user) for user in users],
            total=total,
            page=page,
            per_page=per_page,
            pages=pages
        )
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при получении списка пользователей: {str(e)}"
        )


@router.get("/users/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    current_user: User = Depends(get_admin_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Получить информацию о пользователе (только для админов)"""
    try:
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalars().first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Пользователь не найден"
            )
        
        return UserResponse.from_orm(user)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при получении пользователя: {str(e)}"
        )


@router.put("/users/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_data: AdminUserUpdate,
    current_user: User = Depends(get_admin_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Обновить пользователя (только для админов)"""
    try:
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalars().first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Пользователь не найден"
            )
        
        # Проверяем, что админ не может изменить свою роль
        if user.id == current_user.id and user_data.role and user_data.role != current_user.role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Нельзя изменить свою собственную роль"
            )
        
        # Проверяем уникальность username и email
        if user_data.username and user_data.username != user.username:
            existing_result = await db.execute(
                select(User).where(User.username == user_data.username, User.id != user_id)
            )
            if existing_result.scalars().first():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Пользователь с таким именем уже существует"
                )
        
        if user_data.email and user_data.email != user.email:
            existing_result = await db.execute(
                select(User).where(User.email == user_data.email, User.id != user_id)
            )
            if existing_result.scalars().first():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Пользователь с таким email уже существует"
                )
        
        # Обновляем поля
        for field, value in user_data.dict(exclude_unset=True).items():
            setattr(user, field, value)
        
        await db.commit()
        await db.refresh(user)
        
        return UserResponse.from_orm(user)
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при обновлении пользователя: {str(e)}"
        )


@router.delete("/users/{user_id}")
async def delete_user(
    user_id: int,
    current_user: User = Depends(get_admin_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Удалить пользователя (только для админов)"""
    try:
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Нельзя удалить самого себя"
            )
        
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalars().first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Пользователь не найден"
            )
        
        # Помечаем как удаленного вместо физического удаления
        user.status = UserStatus.DELETED
        await db.commit()
        
        return {"message": "Пользователь успешно удален"}
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при удалении пользователя: {str(e)}"
        )


@router.get("/stats")
async def get_admin_stats(
    current_user: User = Depends(get_admin_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Получить статистику для админ панели"""
    try:
        # Общее количество пользователей
        total_users_result = await db.execute(select(func.count(User.id)))
        total_users = total_users_result.scalar()
        
        # Активные пользователи
        active_users_result = await db.execute(
            select(func.count(User.id)).where(User.status == UserStatus.ACTIVE)
        )
        active_users = active_users_result.scalar()
        
        # Пользователи в ожидании
        pending_users_result = await db.execute(
            select(func.count(User.id)).where(User.status == UserStatus.PENDING)
        )
        pending_users = pending_users_result.scalar()
        
        # Пользователи с OTP
        otp_users_result = await db.execute(
            select(func.count(User.id)).where(User.is_otp_enabled == True)
        )
        otp_users = otp_users_result.scalar()
        
        # Статистика по ролям
        admin_count_result = await db.execute(
            select(func.count(User.id)).where(User.role == UserRole.ADMIN)
        )
        admin_count = admin_count_result.scalar()
        
        user_count_result = await db.execute(
            select(func.count(User.id)).where(User.role == UserRole.USER)
        )
        user_count = user_count_result.scalar()
        
        return {
            "total_users": total_users,
            "active_users": active_users,
            "pending_users": pending_users,
            "otp_enabled_users": otp_users,
            "roles": {
                "admin": admin_count,
                "user": user_count
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при получении статистики: {str(e)}"
        )
