"""
API эндпоинты для торговых операций
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from app.core.database import get_db
from app.core.exchange_manager import exchange_manager

logger = structlog.get_logger()
router = APIRouter()


@router.get("/portfolio")
async def get_portfolio():
    """Получить портфель"""
    try:
        # Получить балансы со всех подключенных бирж
        portfolio = {}
        
        for exchange_name in ['binance', 'bybit']:
            try:
                balance = await exchange_manager.get_balance(exchange_name)
                portfolio[exchange_name] = balance
            except Exception as e:
                logger.warning(f"Не удалось получить баланс с {exchange_name}: {e}")
                portfolio[exchange_name] = {"error": str(e)}
        
        return portfolio
        
    except Exception as e:
        logger.error(f"Ошибка получения портфеля: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка получения портфеля"
        )


@router.get("/positions")
async def get_positions(exchange: str = "binance"):
    """Получить позиции"""
    try:
        positions = await exchange_manager.get_positions(exchange)
        return {"exchange": exchange, "positions": positions}
        
    except Exception as e:
        logger.error(f"Ошибка получения позиций с {exchange}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения позиций с {exchange}"
        )


@router.get("/orders")
async def get_orders(
    exchange: str = "binance",
    symbol: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """Получить ордера"""
    try:
        # Получить открытые ордера с биржи
        open_orders = await exchange_manager.get_open_orders(exchange, symbol)
        
        return {
            "exchange": exchange,
            "symbol": symbol,
            "open_orders": open_orders
        }
        
    except Exception as e:
        logger.error(f"Ошибка получения ордеров: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка получения ордеров"
        )


@router.get("/trades")
async def get_trades(
    strategy_id: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """Получить сделки"""
    try:
        # Здесь будет логика получения сделок из БД
        # Пока возвращаем заглушку
        return {
            "strategy_id": strategy_id,
            "trades": [],
            "message": "Получение сделок будет реализовано позже"
        }
        
    except Exception as e:
        logger.error(f"Ошибка получения сделок: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка получения сделок"
        )


@router.get("/market-data/{symbol}")
async def get_market_data(symbol: str, exchange: str = "binance"):
    """Получить рыночные данные"""
    try:
        # Получить тикер
        ticker = await exchange_manager.get_ticker(exchange, symbol)
        
        # Получить стакан
        orderbook = await exchange_manager.get_orderbook(exchange, symbol, limit=10)
        
        return {
            "symbol": symbol,
            "exchange": exchange,
            "ticker": ticker,
            "orderbook": orderbook
        }
        
    except Exception as e:
        logger.error(f"Ошибка получения рыночных данных для {symbol}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения рыночных данных для {symbol}"
        )
