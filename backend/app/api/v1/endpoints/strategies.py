"""
API эндпоинты для управления стратегиями
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
import structlog

from app.core.database import get_db
from app.core.trading_engine import trading_engine
from app.schemas.strategy import StrategyCreate, StrategyResponse, StrategyUpdate
from app.services.strategy_service import StrategyService

logger = structlog.get_logger()
router = APIRouter()


@router.post("/", response_model=StrategyResponse)
async def create_strategy(
    strategy_data: StrategyCreate,
    db: AsyncSession = Depends(get_db)
):
    """Создать новую стратегию"""
    try:
        strategy_service = StrategyService(db)
        strategy = await strategy_service.create_strategy(strategy_data)
        
        logger.info(f"Создана стратегия: {strategy.name}")
        return strategy
        
    except Exception as e:
        logger.error(f"Ошибка создания стратегии: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/", response_model=List[StrategyResponse])
async def get_strategies(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """Получить список стратегий"""
    try:
        strategy_service = StrategyService(db)
        strategies = await strategy_service.get_strategies(skip=skip, limit=limit)
        return strategies
        
    except Exception as e:
        logger.error(f"Ошибка получения стратегий: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка получения стратегий"
        )


@router.get("/{strategy_id}", response_model=StrategyResponse)
async def get_strategy(
    strategy_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Получить стратегию по ID"""
    try:
        strategy_service = StrategyService(db)
        strategy = await strategy_service.get_strategy(strategy_id)
        
        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Стратегия не найдена"
            )
        
        return strategy
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка получения стратегии {strategy_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка получения стратегии"
        )


@router.put("/{strategy_id}", response_model=StrategyResponse)
async def update_strategy(
    strategy_id: str,
    strategy_data: StrategyUpdate,
    db: AsyncSession = Depends(get_db)
):
    """Обновить стратегию"""
    try:
        strategy_service = StrategyService(db)
        strategy = await strategy_service.update_strategy(strategy_id, strategy_data)
        
        if not strategy:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Стратегия не найдена"
            )
        
        logger.info(f"Обновлена стратегия: {strategy_id}")
        return strategy
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка обновления стратегии {strategy_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{strategy_id}")
async def delete_strategy(
    strategy_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Удалить стратегию"""
    try:
        # Сначала остановить стратегию если она активна
        await trading_engine.stop_strategy(strategy_id)
        
        strategy_service = StrategyService(db)
        success = await strategy_service.delete_strategy(strategy_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Стратегия не найдена"
            )
        
        logger.info(f"Удалена стратегия: {strategy_id}")
        return {"message": "Стратегия удалена"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка удаления стратегии {strategy_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка удаления стратегии"
        )


@router.post("/{strategy_id}/start")
async def start_strategy(
    strategy_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Запустить стратегию"""
    try:
        success = await trading_engine.start_strategy(strategy_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Не удалось запустить стратегию"
            )
        
        logger.info(f"Запущена стратегия: {strategy_id}")
        return {"message": "Стратегия запущена", "strategy_id": strategy_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка запуска стратегии {strategy_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка запуска стратегии"
        )


@router.post("/{strategy_id}/stop")
async def stop_strategy(
    strategy_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Остановить стратегию"""
    try:
        success = await trading_engine.stop_strategy(strategy_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Не удалось остановить стратегию"
            )
        
        logger.info(f"Остановлена стратегия: {strategy_id}")
        return {"message": "Стратегия остановлена", "strategy_id": strategy_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка остановки стратегии {strategy_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка остановки стратегии"
        )


@router.post("/{strategy_id}/pause")
async def pause_strategy(
    strategy_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Приостановить стратегию"""
    try:
        success = await trading_engine.pause_strategy(strategy_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Не удалось приостановить стратегию"
            )
        
        logger.info(f"Приостановлена стратегия: {strategy_id}")
        return {"message": "Стратегия приостановлена", "strategy_id": strategy_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка приостановки стратегии {strategy_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка приостановки стратегии"
        )


@router.post("/{strategy_id}/resume")
async def resume_strategy(
    strategy_id: str,
    db: AsyncSession = Depends(get_db)
):
    """Возобновить стратегию"""
    try:
        success = await trading_engine.resume_strategy(strategy_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Не удалось возобновить стратегию"
            )
        
        logger.info(f"Возобновлена стратегия: {strategy_id}")
        return {"message": "Стратегия возобновлена", "strategy_id": strategy_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка возобновления стратегии {strategy_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка возобновления стратегии"
        )


@router.get("/{strategy_id}/status")
async def get_strategy_status(strategy_id: str):
    """Получить статус стратегии"""
    try:
        status_data = await trading_engine.get_strategy_status(strategy_id)
        
        if not status_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Стратегия не найдена или не активна"
            )
        
        return status_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка получения статуса стратегии {strategy_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка получения статуса стратегии"
        )
