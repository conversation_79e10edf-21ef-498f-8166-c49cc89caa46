"""
Главный роутер API v1
"""
from fastapi import APIRouter

from app.api.v1.endpoints import exchanges, auth, admin
from app.api.v1 import bots, exchange_keys

api_router = APIRouter()

# Подключение эндпоинтов
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
api_router.include_router(exchange_keys.router, prefix="/exchange-keys", tags=["exchange-keys"])
api_router.include_router(bots.router, prefix="/bots", tags=["bots"])
api_router.include_router(exchanges.router, prefix="/exchanges", tags=["exchanges"])
