"""
Главный роутер API v1
"""
from fastapi import APIRouter

from app.api.v1.endpoints import strategies, trading, exchanges, auth
from app.api.v1 import bots, exchange_keys

api_router = APIRouter()

# Подключение эндпоинтов
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])
api_router.include_router(exchange_keys.router, prefix="/exchange-keys", tags=["exchange-keys"])
api_router.include_router(bots.router, prefix="/bots", tags=["bots"])
api_router.include_router(strategies.router, prefix="/strategies", tags=["strategies"])
api_router.include_router(trading.router, prefix="/trading", tags=["trading"])
api_router.include_router(exchanges.router, prefix="/exchanges", tags=["exchanges"])
