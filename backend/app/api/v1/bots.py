"""
API эндпоинты для управления торговыми ботами.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.database import get_db
from app.models.bot import GridBot
from app.models.order import Order
from app.schemas.bot import BotCreate, BotUpdate, BotResponse
from app.core.trading_engine import trading_engine

import structlog

logger = structlog.get_logger()

router = APIRouter()


@router.get("/", response_model=List[BotResponse])
async def get_bots(
    skip: int = 0,
    limit: int = 100,
    active_only: bool = False,
    db: Session = Depends(get_db)
):
    """Получить список всех ботов."""
    query = db.query(GridBot)
    
    if active_only:
        query = query.filter(GridBot.is_active == True)
    
    bots = query.order_by(desc(GridBot.created_at)).offset(skip).limit(limit).all()
    return bots


@router.get("/{bot_id}", response_model=BotResponse)
async def get_bot(bot_id: int, db: Session = Depends(get_db)):
    """Получить информацию о конкретном боте."""
    bot = db.query(GridBot).filter(GridBot.id == bot_id).first()
    if not bot:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Бот не найден"
        )
    return bot


@router.post("/", response_model=BotResponse)
async def create_bot(bot_data: BotCreate, db: Session = Depends(get_db)):
    """Создать нового торгового бота."""
    try:
        # Валидация параметров
        if bot_data.lower_price >= bot_data.upper_price:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Нижняя граница должна быть меньше верхней"
            )
        
        if bot_data.grid_count < 2:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Количество уровней сетки должно быть не менее 2"
            )
        
        if bot_data.order_amount <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Размер ордера должен быть больше 0"
            )
        
        # Создание бота
        bot = GridBot(**bot_data.dict())
        db.add(bot)
        db.commit()
        db.refresh(bot)
        
        logger.info(f"Создан новый бот: {bot.name} (ID: {bot.id})")
        return bot
        
    except Exception as e:
        db.rollback()
        logger.error(f"Ошибка создания бота: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка создания бота: {str(e)}"
        )


@router.put("/{bot_id}", response_model=BotResponse)
async def update_bot(
    bot_id: int,
    bot_data: BotUpdate,
    db: Session = Depends(get_db)
):
    """Обновить параметры бота."""
    bot = db.query(GridBot).filter(GridBot.id == bot_id).first()
    if not bot:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Бот не найден"
        )
    
    # Проверяем, что бот не активен
    if bot.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Нельзя изменять параметры активного бота"
        )
    
    try:
        # Обновляем только переданные поля
        update_data = bot_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(bot, field, value)
        
        db.commit()
        db.refresh(bot)
        
        logger.info(f"Обновлен бот: {bot.name} (ID: {bot.id})")
        return bot
        
    except Exception as e:
        db.rollback()
        logger.error(f"Ошибка обновления бота {bot_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка обновления бота: {str(e)}"
        )


@router.post("/{bot_id}/start")
async def start_bot(bot_id: int, db: Session = Depends(get_db)):
    """Запустить торгового бота."""
    bot = db.query(GridBot).filter(GridBot.id == bot_id).first()
    if not bot:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Бот не найден"
        )
    
    if bot.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Бот уже активен"
        )
    
    try:
        # Запускаем бота через торговый движок
        success = await trading_engine.start_bot(bot_id)
        
        if success:
            bot.is_active = True
            from sqlalchemy.sql import func
            bot.started_at = func.now()
            bot.stopped_at = None
            db.commit()
            
            logger.info(f"Запущен бот: {bot.name} (ID: {bot.id})")
            return {"message": "Бот успешно запущен", "bot_id": bot_id}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Не удалось запустить бота"
            )
            
    except Exception as e:
        logger.error(f"Ошибка запуска бота {bot_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка запуска бота: {str(e)}"
        )


@router.post("/{bot_id}/stop")
async def stop_bot(bot_id: int, db: Session = Depends(get_db)):
    """Остановить торгового бота."""
    bot = db.query(GridBot).filter(GridBot.id == bot_id).first()
    if not bot:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Бот не найден"
        )
    
    if not bot.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Бот уже остановлен"
        )
    
    try:
        # Останавливаем бота через торговый движок
        success = await trading_engine.stop_bot(bot_id)
        
        if success:
            bot.is_active = False
            from sqlalchemy.sql import func
            bot.stopped_at = func.now()
            db.commit()
            
            logger.info(f"Остановлен бот: {bot.name} (ID: {bot.id})")
            return {"message": "Бот успешно остановлен", "bot_id": bot_id}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Не удалось остановить бота"
            )
            
    except Exception as e:
        logger.error(f"Ошибка остановки бота {bot_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка остановки бота: {str(e)}"
        )


@router.delete("/{bot_id}")
async def delete_bot(bot_id: int, db: Session = Depends(get_db)):
    """Удалить торгового бота."""
    bot = db.query(GridBot).filter(GridBot.id == bot_id).first()
    if not bot:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Бот не найден"
        )
    
    if bot.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Нельзя удалить активного бота. Сначала остановите его."
        )
    
    try:
        db.delete(bot)
        db.commit()
        
        logger.info(f"Удален бот: {bot.name} (ID: {bot.id})")
        return {"message": "Бот успешно удален", "bot_id": bot_id}
        
    except Exception as e:
        db.rollback()
        logger.error(f"Ошибка удаления бота {bot_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка удаления бота: {str(e)}"
        )


@router.get("/{bot_id}/orders", response_model=List[dict])
async def get_bot_orders(
    bot_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Получить ордера бота."""
    bot = db.query(GridBot).filter(GridBot.id == bot_id).first()
    if not bot:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Бот не найден"
        )
    
    orders = db.query(Order).filter(
        Order.bot_id == bot_id
    ).order_by(desc(Order.created_at)).offset(skip).limit(limit).all()
    
    return [
        {
            "id": order.id,
            "symbol": order.symbol,
            "order_type": order.order_type.value,
            "amount": float(order.amount),
            "price": float(order.price),
            "status": order.status.value,
            "filled_amount": float(order.filled_amount),
            "created_at": order.created_at,
            "filled_at": order.filled_at
        }
        for order in orders
    ]


@router.get("/{bot_id}/stats")
async def get_bot_stats(bot_id: int, db: Session = Depends(get_db)):
    """Получить статистику бота."""
    bot = db.query(GridBot).filter(GridBot.id == bot_id).first()
    if not bot:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Бот не найден"
        )
    
    # Подсчитываем статистику ордеров
    total_orders = db.query(Order).filter(Order.bot_id == bot_id).count()
    filled_orders = db.query(Order).filter(
        Order.bot_id == bot_id,
        Order.status == "filled"
    ).count()
    
    return {
        "bot_id": bot_id,
        "name": bot.name,
        "symbol": bot.symbol,
        "is_active": bot.is_active,
        "total_profit": float(bot.total_profit),
        "total_trades": bot.total_trades,
        "total_orders": total_orders,
        "filled_orders": filled_orders,
        "grid_levels": bot.get_grid_levels(),
        "price_range": float(bot.price_range),
        "grid_step": float(bot.grid_step),
        "created_at": bot.created_at,
        "started_at": bot.started_at,
        "stopped_at": bot.stopped_at
    }
