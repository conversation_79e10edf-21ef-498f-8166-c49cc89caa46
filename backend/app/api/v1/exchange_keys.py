"""
API эндпоинты для управления ключами бирж.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import desc
from sqlalchemy.sql import func

from app.database import get_db
from app.models.exchange_key import ExchangeKey, ExchangeType, NetworkType
from app.schemas.exchange_key import (
    ExchangeKeyCreate, ExchangeKeyUpdate, ExchangeKeyResponse,
    ExchangeKeyValidation, ExchangeInfo
)
from app.services.exchange_service import exchange_service

import structlog

logger = structlog.get_logger()

router = APIRouter()


@router.get("/exchanges", response_model=List[ExchangeInfo])
async def get_supported_exchanges():
    """Получить список поддерживаемых бирж."""
    try:
        exchanges = await exchange_service.get_supported_exchanges()
        return exchanges
    except Exception as e:
        logger.error(f"Ошибка получения списка бирж: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Ошибка получения списка бирж"
        )


@router.get("/", response_model=List[ExchangeKeyResponse])
async def get_exchange_keys(
    skip: int = 0,
    limit: int = 100,
    exchange: Optional[ExchangeType] = None,
    network: Optional[NetworkType] = None,
    active_only: bool = False,
    db: Session = Depends(get_db)
):
    """Получить список API ключей."""
    query = db.query(ExchangeKey)
    
    if exchange:
        query = query.filter(ExchangeKey.exchange == exchange)
    
    if network:
        query = query.filter(ExchangeKey.network == network)
    
    if active_only:
        query = query.filter(ExchangeKey.is_active == True)
    
    keys = query.order_by(desc(ExchangeKey.created_at)).offset(skip).limit(limit).all()
    return keys


@router.get("/{key_id}", response_model=ExchangeKeyResponse)
async def get_exchange_key(key_id: int, db: Session = Depends(get_db)):
    """Получить информацию о конкретном ключе."""
    key = db.query(ExchangeKey).filter(ExchangeKey.id == key_id).first()
    if not key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ключ не найден"
        )
    return key


@router.post("/", response_model=ExchangeKeyResponse)
async def create_exchange_key(key_data: ExchangeKeyCreate, db: Session = Depends(get_db)):
    """Создать новый API ключ."""
    try:
        # Проверяем уникальность названия
        existing_key = db.query(ExchangeKey).filter(
            ExchangeKey.name == key_data.name
        ).first()
        
        if existing_key:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Ключ с таким названием уже существует"
            )
        
        # Создаем ключ
        key = ExchangeKey(
            name=key_data.name,
            exchange=key_data.exchange,
            network=key_data.network
        )
        
        # Устанавливаем зашифрованные данные
        key.api_key = key_data.api_key
        key.secret = key_data.secret
        if key_data.passphrase:
            key.passphrase = key_data.passphrase
        
        db.add(key)
        db.commit()
        db.refresh(key)
        
        logger.info(f"Создан новый ключ: {key.name} для {key.exchange} ({key.network})")
        
        # Запускаем валидацию в фоне
        try:
            validation_result = await exchange_service.validate_api_keys(key)
            
            key.is_validated = validation_result['valid']
            if not validation_result['valid']:
                key.validation_error = validation_result['error']
            else:
                key.validation_error = None
                # Сохраняем баланс
                key.balance = validation_result['balance']
            
            key.last_validation = func.now()
            db.commit()
            
        except Exception as e:
            logger.error(f"Ошибка валидации ключа {key.id}: {e}")
            key.validation_error = f"Ошибка валидации: {str(e)}"
            db.commit()
        
        return key
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Ошибка создания ключа: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка создания ключа: {str(e)}"
        )


@router.put("/{key_id}", response_model=ExchangeKeyResponse)
async def update_exchange_key(
    key_id: int,
    key_data: ExchangeKeyUpdate,
    db: Session = Depends(get_db)
):
    """Обновить API ключ."""
    key = db.query(ExchangeKey).filter(ExchangeKey.id == key_id).first()
    if not key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ключ не найден"
        )
    
    try:
        # Обновляем только переданные поля
        update_data = key_data.dict(exclude_unset=True)
        
        # Проверяем уникальность названия
        if 'name' in update_data and update_data['name'] != key.name:
            existing_key = db.query(ExchangeKey).filter(
                ExchangeKey.name == update_data['name'],
                ExchangeKey.id != key_id
            ).first()
            
            if existing_key:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Ключ с таким названием уже существует"
                )
        
        # Обновляем поля
        for field, value in update_data.items():
            if field in ['api_key', 'secret', 'passphrase']:
                # Используем свойства для шифрования
                setattr(key, field, value)
            else:
                setattr(key, field, value)
        
        # Сбрасываем валидацию если изменились ключи
        if any(field in update_data for field in ['api_key', 'secret', 'passphrase']):
            key.is_validated = False
            key.validation_error = None
            key.last_validation = None
            # Очищаем кэш биржи
            exchange_service.clear_cache(key_id)
        
        db.commit()
        db.refresh(key)
        
        logger.info(f"Обновлен ключ: {key.name} (ID: {key.id})")
        return key
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Ошибка обновления ключа {key_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка обновления ключа: {str(e)}"
        )


@router.post("/{key_id}/validate", response_model=ExchangeKeyValidation)
async def validate_exchange_key(key_id: int, db: Session = Depends(get_db)):
    """Валидировать API ключ."""
    key = db.query(ExchangeKey).filter(ExchangeKey.id == key_id).first()
    if not key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ключ не найден"
        )
    
    try:
        # Очищаем кэш перед валидацией
        exchange_service.clear_cache(key_id)
        
        # Валидируем ключ
        validation_result = await exchange_service.validate_api_keys(key)
        
        # Обновляем статус в БД
        key.is_validated = validation_result['valid']
        key.validation_error = validation_result['error']
        key.last_validation = func.now()
        
        if validation_result['valid']:
            # Сохраняем баланс
            key.balance = validation_result['balance']
        
        db.commit()
        
        return ExchangeKeyValidation(
            key_id=key_id,
            valid=validation_result['valid'],
            error=validation_result['error'],
            balance=validation_result['balance'],
            permissions=validation_result['permissions'],
            exchange_info=validation_result['exchange_info']
        )
        
    except Exception as e:
        logger.error(f"Ошибка валидации ключа {key_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка валидации: {str(e)}"
        )


@router.get("/{key_id}/balance")
async def get_key_balance(
    key_id: int,
    force_refresh: bool = False,
    db: Session = Depends(get_db)
):
    """Получить баланс по ключу."""
    key = db.query(ExchangeKey).filter(ExchangeKey.id == key_id).first()
    if not key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ключ не найден"
        )
    
    if not key.is_validated:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Ключ не прошел валидацию"
        )
    
    try:
        balance_result = await exchange_service.get_balance(key_id, force_refresh)
        
        if 'error' in balance_result:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=balance_result['error']
            )
        
        return balance_result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Ошибка получения баланса для ключа {key_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка получения баланса: {str(e)}"
        )


@router.delete("/{key_id}")
async def delete_exchange_key(key_id: int, db: Session = Depends(get_db)):
    """Удалить API ключ."""
    key = db.query(ExchangeKey).filter(ExchangeKey.id == key_id).first()
    if not key:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Ключ не найден"
        )
    
    # Проверяем, что ключ не используется активными ботами
    from app.models.bot import GridBot
    active_bots = db.query(GridBot).filter(
        GridBot.exchange_key_id == key_id,
        GridBot.is_active == True
    ).count()
    
    if active_bots > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Нельзя удалить ключ, используемый {active_bots} активными ботами"
        )
    
    try:
        # Очищаем кэш
        exchange_service.clear_cache(key_id)
        
        # Удаляем ключ
        db.delete(key)
        db.commit()
        
        logger.info(f"Удален ключ: {key.name} (ID: {key_id})")
        return {"message": "Ключ успешно удален", "key_id": key_id}
        
    except Exception as e:
        db.rollback()
        logger.error(f"Ошибка удаления ключа {key_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка удаления ключа: {str(e)}"
        )
