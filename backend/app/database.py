"""
Database configuration and session management.
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from typing import Generator, AsyncGenerator

from app.core.config import settings

# Создаем синхронный движок базы данных для миграций
engine = create_engine(
    settings.DATABASE_URL,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=False  # Установите True для отладки SQL запросов
)

# Создаем асинхронный движок базы данных
async_database_url = settings.DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
async_engine = create_async_engine(
    async_database_url,
    pool_pre_ping=True,
    pool_recycle=300,
    echo=False
)

# Создаем фабрику синхронных сессий (для миграций)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Создаем фабрику асинхронных сессий
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False
)

# Базовый класс для моделей
Base = declarative_base()


def get_db() -> Generator[Session, None, None]:
    """
    Генератор синхронных сессий базы данных.
    Используется для миграций и других синхронных операций.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Генератор асинхронных сессий базы данных.
    Используется как зависимость в FastAPI для async эндпоинтов.
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()


def create_tables():
    """
    Создает все таблицы в базе данных.
    """
    Base.metadata.create_all(bind=engine)


def drop_tables():
    """
    Удаляет все таблицы из базы данных.
    """
    Base.metadata.drop_all(bind=engine)
