"""
Celery configuration for the trading bot application.
"""

from celery import Celery
from app.core.config import settings

# Создаем экземпляр Celery
celery_app = Celery(
    "trading_bot",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
    include=["app.tasks"]
)

# Конфигурация Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 минут
    task_soft_time_limit=25 * 60,  # 25 минут
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Настройки для beat scheduler
celery_app.conf.beat_schedule = {
    'check-grid-bots': {
        'task': 'app.tasks.check_grid_bots',
        'schedule': 30.0,  # каждые 30 секунд
    },
    'update-market-data': {
        'task': 'app.tasks.update_market_data',
        'schedule': 60.0,  # каждую минуту
    },
}

if __name__ == "__main__":
    celery_app.start()
