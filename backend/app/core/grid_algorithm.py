"""
Алгоритм сеточной торговли
"""
import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime
import structlog
import numpy as np

from app.models import Strategy, Order, Trade
from app.models.enums import OrderSide, OrderType, OrderStatus, StrategyStatus, TradeType
from app.core.database import AsyncSessionLocal

logger = structlog.get_logger()


class GridLevel:
    """Уровень сетки"""
    def __init__(self, price: float, amount: float, side: str, level: int):
        self.price = price
        self.amount = amount
        self.side = side  # buy или sell
        self.level = level
        self.order_id: Optional[str] = None
        self.is_filled = False


class GridAlgorithm:
    """Алгоритм сеточной торговли"""
    
    def __init__(self, strategy: Strategy, exchange_manager):
        self.strategy = strategy
        self.exchange_manager = exchange_manager
        self.grid_levels: List[GridLevel] = []
        self.running = False
        self.paused = False
        self.monitor_task: Optional[asyncio.Task] = None
        
        # Статистика
        self.total_profit = 0.0
        self.total_trades = 0
        self.current_position = 0.0
        self.average_entry_price = 0.0
    
    async def start(self) -> bool:
        """Запустить алгоритм"""
        try:
            logger.info(f"Запуск алгоритма сетки для стратегии {self.strategy.name}")
            
            # Подключиться к бирже
            exchange = await self.exchange_manager.get_exchange(self.strategy.exchange)
            
            # Настроить плечо и режим маржи
            await self._setup_trading_parameters()
            
            # Получить текущую цену
            ticker = await exchange.fetch_ticker(self.strategy.symbol)
            current_price = ticker['last']
            
            # Создать сетку
            await self._create_grid(current_price)
            
            # Разместить начальные ордера
            await self._place_initial_orders()
            
            # Запустить мониторинг
            self.running = True
            self.monitor_task = asyncio.create_task(self._monitor_orders())
            
            logger.info(f"Алгоритм сетки запущен для {self.strategy.symbol}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка запуска алгоритма сетки: {e}")
            return False
    
    async def stop(self):
        """Остановить алгоритм"""
        logger.info(f"Остановка алгоритма сетки для стратегии {self.strategy.name}")
        
        self.running = False
        
        # Остановить мониторинг
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        # Отменить все открытые ордера
        await self._cancel_all_orders()
        
        # Закрыть позицию если нужно
        if abs(self.current_position) > 0:
            await self._close_position()
    
    async def pause(self):
        """Приостановить алгоритм"""
        self.paused = True
        await self._cancel_all_orders()
        logger.info(f"Алгоритм сетки приостановлен для {self.strategy.name}")
    
    async def resume(self):
        """Возобновить алгоритм"""
        self.paused = False
        await self._place_grid_orders()
        logger.info(f"Алгоритм сетки возобновлен для {self.strategy.name}")
    
    async def is_healthy(self) -> bool:
        """Проверить здоровье алгоритма"""
        try:
            # Проверить подключение к бирже
            exchange = await self.exchange_manager.get_exchange(self.strategy.exchange)
            await exchange.fetch_ticker(self.strategy.symbol)
            
            # Проверить открытые ордера
            open_orders = await exchange.fetch_open_orders(self.strategy.symbol)
            
            return True
        except Exception as e:
            logger.error(f"Проблема со здоровьем алгоритма: {e}")
            return False
    
    async def get_status(self) -> Dict[str, Any]:
        """Получить статус алгоритма"""
        return {
            'strategy_id': str(self.strategy.id),
            'running': self.running,
            'paused': self.paused,
            'grid_levels': len(self.grid_levels),
            'current_position': self.current_position,
            'average_entry_price': self.average_entry_price,
            'total_profit': self.total_profit,
            'total_trades': self.total_trades
        }
    
    async def _setup_trading_parameters(self):
        """Настроить параметры торговли"""
        try:
            exchange = await self.exchange_manager.get_exchange(self.strategy.exchange)
            
            # Установить плечо
            if self.strategy.leverage > 1:
                await self.exchange_manager.set_leverage(
                    self.strategy.exchange,
                    self.strategy.symbol,
                    self.strategy.leverage
                )
            
            # Установить режим маржи
            await self.exchange_manager.set_margin_mode(
                self.strategy.exchange,
                self.strategy.symbol,
                self.strategy.margin_mode
            )
            
        except Exception as e:
            logger.warning(f"Не удалось настроить параметры торговли: {e}")
    
    async def _create_grid(self, current_price: float):
        """Создать сетку уровней"""
        self.grid_levels = []
        
        # Рассчитать уровни сетки
        step_size = current_price * (self.strategy.grid_step_percent / 100)
        
        # Создать уровни выше текущей цены (для продажи)
        for i in range(1, self.strategy.grid_count // 2 + 1):
            price = current_price + (step_size * i)
            amount = self._calculate_grid_amount(i)
            level = GridLevel(price, amount, OrderSide.SELL, i)
            self.grid_levels.append(level)
        
        # Создать уровни ниже текущей цены (для покупки)
        for i in range(1, self.strategy.grid_count // 2 + 1):
            price = current_price - (step_size * i)
            amount = self._calculate_grid_amount(i)
            level = GridLevel(price, amount, OrderSide.BUY, -i)
            self.grid_levels.append(level)
        
        logger.info(f"Создана сетка из {len(self.grid_levels)} уровней")
    
    def _calculate_grid_amount(self, level: int) -> float:
        """Рассчитать объем для уровня сетки"""
        base_amount = self.strategy.deposit_amount / self.strategy.grid_count
        
        # Применить мультипликатор объема
        if level > 1:
            multiplier = self.strategy.volume_multiplier ** (level - 1)
            return base_amount * multiplier
        
        return base_amount
    
    async def _place_initial_orders(self):
        """Разместить начальные ордера"""
        await self._place_grid_orders()
    
    async def _place_grid_orders(self):
        """Разместить ордера сетки"""
        if self.paused:
            return
        
        for level in self.grid_levels:
            if not level.is_filled and not level.order_id:
                try:
                    order = await self.exchange_manager.create_order(
                        exchange_name=self.strategy.exchange,
                        symbol=self.strategy.symbol,
                        type=OrderType.LIMIT,
                        side=level.side,
                        amount=level.amount,
                        price=level.price
                    )
                    
                    level.order_id = order['id']
                    
                    # Сохранить ордер в БД
                    await self._save_order_to_db(order, level)
                    
                except Exception as e:
                    logger.error(f"Ошибка размещения ордера на уровне {level.level}: {e}")
    
    async def _monitor_orders(self):
        """Мониторинг ордеров"""
        while self.running:
            try:
                if not self.paused:
                    await self._check_filled_orders()
                    await self._update_grid()
                
                await asyncio.sleep(2)  # Проверка каждые 2 секунды
                
            except Exception as e:
                logger.error(f"Ошибка мониторинга ордеров: {e}")
                await asyncio.sleep(5)
    
    async def _check_filled_orders(self):
        """Проверить исполненные ордера"""
        for level in self.grid_levels:
            if level.order_id and not level.is_filled:
                try:
                    order_status = await self.exchange_manager.get_order_status(
                        self.strategy.exchange,
                        level.order_id,
                        self.strategy.symbol
                    )
                    
                    if order_status['status'] == 'closed':
                        await self._handle_filled_order(level, order_status)
                        
                except Exception as e:
                    logger.error(f"Ошибка проверки ордера {level.order_id}: {e}")
    
    async def _handle_filled_order(self, level: GridLevel, order_data: Dict):
        """Обработать исполненный ордер"""
        level.is_filled = True
        
        # Обновить позицию
        if level.side == OrderSide.BUY:
            self.current_position += level.amount
        else:
            self.current_position -= level.amount
        
        # Обновить среднюю цену входа
        self._update_average_entry_price(level, order_data)
        
        # Сохранить сделку в БД
        await self._save_trade_to_db(level, order_data)
        
        # Разместить новый ордер на противоположной стороне
        await self._place_opposite_order(level)
        
        # Проверить условия Take Profit
        await self._check_take_profit()
        
        self.total_trades += 1
        logger.info(f"Ордер исполнен: {level.side} {level.amount} по цене {order_data['price']}")
    
    def _update_average_entry_price(self, level: GridLevel, order_data: Dict):
        """Обновить среднюю цену входа"""
        if self.current_position != 0:
            total_cost = abs(self.current_position) * self.average_entry_price
            new_cost = level.amount * order_data['price']
            
            if level.side == OrderSide.BUY:
                total_cost += new_cost
            else:
                total_cost -= new_cost
            
            self.average_entry_price = total_cost / abs(self.current_position)
    
    async def _place_opposite_order(self, filled_level: GridLevel):
        """Разместить ордер на противоположной стороне"""
        # Логика размещения противоположного ордера
        # Это упрощенная версия - в реальности нужна более сложная логика
        pass
    
    async def _check_take_profit(self):
        """Проверить условия Take Profit"""
        if abs(self.current_position) == 0:
            return
        
        try:
            ticker = await self.exchange_manager.get_ticker(
                self.strategy.exchange,
                self.strategy.symbol
            )
            current_price = ticker['last']
            
            # Рассчитать прибыль
            if self.current_position > 0:  # Длинная позиция
                profit_percent = ((current_price - self.average_entry_price) / self.average_entry_price) * 100
            else:  # Короткая позиция
                profit_percent = ((self.average_entry_price - current_price) / self.average_entry_price) * 100
            
            # Проверить Take Profit
            if profit_percent >= self.strategy.tp_percent:
                await self._execute_take_profit(current_price)
            
            # Проверить Stop Loss
            if self.strategy.sl_percent and profit_percent <= -self.strategy.sl_percent:
                await self._execute_stop_loss(current_price)
                
        except Exception as e:
            logger.error(f"Ошибка проверки Take Profit: {e}")
    
    async def _execute_take_profit(self, current_price: float):
        """Исполнить Take Profit"""
        logger.info(f"Исполнение Take Profit по цене {current_price}")
        await self._close_position()
        await self._restart_grid(current_price)
    
    async def _execute_stop_loss(self, current_price: float):
        """Исполнить Stop Loss"""
        logger.info(f"Исполнение Stop Loss по цене {current_price}")
        await self._close_position()
        # Остановить стратегию при Stop Loss
        await self.stop()
    
    async def _close_position(self):
        """Закрыть позицию"""
        if abs(self.current_position) == 0:
            return
        
        try:
            side = OrderSide.SELL if self.current_position > 0 else OrderSide.BUY
            amount = abs(self.current_position)
            
            order = await self.exchange_manager.create_order(
                exchange_name=self.strategy.exchange,
                symbol=self.strategy.symbol,
                type=OrderType.MARKET,
                side=side,
                amount=amount
            )
            
            self.current_position = 0.0
            self.average_entry_price = 0.0
            
            logger.info(f"Позиция закрыта: {side} {amount}")
            
        except Exception as e:
            logger.error(f"Ошибка закрытия позиции: {e}")
    
    async def _restart_grid(self, current_price: float):
        """Перезапустить сетку"""
        await self._cancel_all_orders()
        await self._create_grid(current_price)
        await self._place_grid_orders()
    
    async def _cancel_all_orders(self):
        """Отменить все ордера"""
        for level in self.grid_levels:
            if level.order_id and not level.is_filled:
                try:
                    await self.exchange_manager.cancel_order(
                        self.strategy.exchange,
                        level.order_id,
                        self.strategy.symbol
                    )
                    level.order_id = None
                except Exception as e:
                    logger.error(f"Ошибка отмены ордера {level.order_id}: {e}")
    
    async def _update_grid(self):
        """Обновить сетку"""
        # Логика обновления сетки при изменении рыночных условий
        pass
    
    async def _save_order_to_db(self, order_data: Dict, level: GridLevel):
        """Сохранить ордер в БД"""
        # Реализация сохранения ордера
        pass
    
    async def _save_trade_to_db(self, level: GridLevel, order_data: Dict):
        """Сохранить сделку в БД"""
        # Реализация сохранения сделки
        pass
