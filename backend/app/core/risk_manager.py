"""
Менеджер рисков
"""
from typing import Dict, List, Optional
import structlog
from datetime import datetime, timedelta

from app.models import Strategy
from app.models.enums import StrategyStatus

logger = structlog.get_logger()


class RiskManager:
    """Менеджер рисков"""
    
    def __init__(self):
        # Глобальные лимиты
        self.max_total_exposure = 100000.0  # Максимальная общая экспозиция в USDT
        self.max_strategies_per_user = 10   # Максимум стратегий на пользователя
        self.max_leverage = 20.0            # Максимальное плечо
        self.max_drawdown_percent = 20.0    # Максимальная просадка %
        
        # Лимиты на стратегию
        self.min_deposit_amount = 10.0      # Минимальный депозит
        self.max_deposit_amount = 50000.0   # Максимальный депозит
        self.max_grid_count = 20            # Максимум уровней сетки
        self.min_grid_step = 0.1            # Минимальный шаг сетки %
        self.max_grid_step = 10.0           # Максимальный шаг сетки %
        
        # Лимиты на символы
        self.allowed_symbols = [
            'BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT',
            'ADA/USDT:USDT', 'SOL/USDT:USDT', 'DOT/USDT:USDT'
        ]
        
        # Лимиты на биржи
        self.allowed_exchanges = ['binance', 'bybit']
    
    async def validate_strategy(self, strategy: Strategy) -> bool:
        """Валидация параметров стратегии"""
        try:
            # Проверить биржу
            if strategy.exchange not in self.allowed_exchanges:
                logger.error(f"Неподдерживаемая биржа: {strategy.exchange}")
                return False
            
            # Проверить символ
            if strategy.symbol not in self.allowed_symbols:
                logger.error(f"Неподдерживаемый символ: {strategy.symbol}")
                return False
            
            # Проверить размер депозита
            if not self._validate_deposit_amount(strategy.deposit_amount):
                return False
            
            # Проверить плечо
            if not self._validate_leverage(strategy.leverage):
                return False
            
            # Проверить параметры сетки
            if not self._validate_grid_parameters(strategy):
                return False
            
            # Проверить риск-менеджмент
            if not self._validate_risk_parameters(strategy):
                return False
            
            logger.info(f"Стратегия {strategy.name} прошла валидацию")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка валидации стратегии: {e}")
            return False
    
    async def monitor_strategy(self, strategy: Strategy) -> bool:
        """Мониторинг стратегии на соответствие рискам"""
        try:
            # Проверить просадку
            if not self._check_drawdown(strategy):
                logger.warning(f"Стратегия {strategy.name} превысила лимит просадки")
                return False
            
            # Проверить общую экспозицию
            if not await self._check_total_exposure(strategy):
                logger.warning(f"Превышена общая экспозиция")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Ошибка мониторинга рисков: {e}")
            return False
    
    def _validate_deposit_amount(self, amount: float) -> bool:
        """Валидация размера депозита"""
        if amount < self.min_deposit_amount:
            logger.error(f"Депозит слишком мал: {amount} < {self.min_deposit_amount}")
            return False
        
        if amount > self.max_deposit_amount:
            logger.error(f"Депозит слишком велик: {amount} > {self.max_deposit_amount}")
            return False
        
        return True
    
    def _validate_leverage(self, leverage: float) -> bool:
        """Валидация плеча"""
        if leverage < 1.0:
            logger.error(f"Плечо не может быть меньше 1: {leverage}")
            return False
        
        if leverage > self.max_leverage:
            logger.error(f"Плечо слишком велико: {leverage} > {self.max_leverage}")
            return False
        
        return True
    
    def _validate_grid_parameters(self, strategy: Strategy) -> bool:
        """Валидация параметров сетки"""
        # Проверить количество уровней
        if strategy.grid_count < 2:
            logger.error(f"Слишком мало уровней сетки: {strategy.grid_count}")
            return False
        
        if strategy.grid_count > self.max_grid_count:
            logger.error(f"Слишком много уровней сетки: {strategy.grid_count} > {self.max_grid_count}")
            return False
        
        # Проверить шаг сетки
        if strategy.grid_step_percent < self.min_grid_step:
            logger.error(f"Шаг сетки слишком мал: {strategy.grid_step_percent} < {self.min_grid_step}")
            return False
        
        if strategy.grid_step_percent > self.max_grid_step:
            logger.error(f"Шаг сетки слишком велик: {strategy.grid_step_percent} > {self.max_grid_step}")
            return False
        
        # Проверить мультипликатор объема
        if strategy.volume_multiplier < 1.0:
            logger.error(f"Мультипликатор объема не может быть меньше 1: {strategy.volume_multiplier}")
            return False
        
        if strategy.volume_multiplier > 5.0:
            logger.error(f"Мультипликатор объема слишком велик: {strategy.volume_multiplier}")
            return False
        
        return True
    
    def _validate_risk_parameters(self, strategy: Strategy) -> bool:
        """Валидация параметров риск-менеджмента"""
        # Проверить Take Profit
        if strategy.tp_percent <= 0:
            logger.error(f"Take Profit должен быть положительным: {strategy.tp_percent}")
            return False
        
        if strategy.tp_percent > 100:
            logger.error(f"Take Profit слишком велик: {strategy.tp_percent}")
            return False
        
        # Проверить Stop Loss
        if strategy.sl_percent is not None:
            if strategy.sl_percent <= 0:
                logger.error(f"Stop Loss должен быть положительным: {strategy.sl_percent}")
                return False
            
            if strategy.sl_percent > 50:
                logger.error(f"Stop Loss слишком велик: {strategy.sl_percent}")
                return False
        
        # Проверить максимальную просадку
        if strategy.max_drawdown_percent <= 0:
            logger.error(f"Максимальная просадка должна быть положительной: {strategy.max_drawdown_percent}")
            return False
        
        if strategy.max_drawdown_percent > self.max_drawdown_percent:
            logger.error(f"Максимальная просадка слишком велика: {strategy.max_drawdown_percent}")
            return False
        
        return True
    
    def _check_drawdown(self, strategy: Strategy) -> bool:
        """Проверить просадку стратегии"""
        if strategy.current_drawdown > strategy.max_drawdown_percent:
            return False
        
        if strategy.current_drawdown > self.max_drawdown_percent:
            return False
        
        return True
    
    async def _check_total_exposure(self, strategy: Strategy) -> bool:
        """Проверить общую экспозицию"""
        # Здесь должна быть логика подсчета общей экспозиции
        # по всем активным стратегиям пользователя
        total_exposure = strategy.deposit_amount * strategy.leverage
        
        if total_exposure > self.max_total_exposure:
            return False
        
        return True
    
    def calculate_position_size(self, strategy: Strategy, current_price: float) -> float:
        """Рассчитать размер позиции"""
        # Базовый размер позиции
        base_size = strategy.deposit_amount / strategy.grid_count
        
        # Применить плечо
        leveraged_size = base_size * strategy.leverage
        
        # Конвертировать в количество контрактов
        position_size = leveraged_size / current_price
        
        return position_size
    
    def calculate_max_loss(self, strategy: Strategy) -> float:
        """Рассчитать максимальный убыток"""
        if strategy.sl_percent:
            return strategy.deposit_amount * (strategy.sl_percent / 100)
        else:
            return strategy.deposit_amount * (strategy.max_drawdown_percent / 100)
    
    def calculate_risk_reward_ratio(self, strategy: Strategy) -> float:
        """Рассчитать соотношение риск/доходность"""
        max_loss = self.calculate_max_loss(strategy)
        max_profit = strategy.deposit_amount * (strategy.tp_percent / 100)
        
        if max_loss > 0:
            return max_profit / max_loss
        
        return 0.0
    
    def get_risk_assessment(self, strategy: Strategy) -> Dict[str, any]:
        """Получить оценку рисков стратегии"""
        return {
            'risk_level': self._assess_risk_level(strategy),
            'max_loss': self.calculate_max_loss(strategy),
            'risk_reward_ratio': self.calculate_risk_reward_ratio(strategy),
            'leverage_risk': 'high' if strategy.leverage > 10 else 'medium' if strategy.leverage > 5 else 'low',
            'grid_risk': 'high' if strategy.grid_count > 15 else 'medium' if strategy.grid_count > 10 else 'low',
            'symbol_volatility': self._get_symbol_volatility(strategy.symbol)
        }
    
    def _assess_risk_level(self, strategy: Strategy) -> str:
        """Оценить уровень риска"""
        risk_score = 0
        
        # Плечо
        if strategy.leverage > 10:
            risk_score += 3
        elif strategy.leverage > 5:
            risk_score += 2
        elif strategy.leverage > 1:
            risk_score += 1
        
        # Количество уровней сетки
        if strategy.grid_count > 15:
            risk_score += 2
        elif strategy.grid_count > 10:
            risk_score += 1
        
        # Размер депозита
        if strategy.deposit_amount > 10000:
            risk_score += 2
        elif strategy.deposit_amount > 1000:
            risk_score += 1
        
        # Stop Loss
        if not strategy.sl_percent:
            risk_score += 2
        
        if risk_score >= 6:
            return 'high'
        elif risk_score >= 3:
            return 'medium'
        else:
            return 'low'
    
    def _get_symbol_volatility(self, symbol: str) -> str:
        """Получить волатильность символа"""
        # Упрощенная оценка волатильности
        high_volatility = ['BTC/USDT:USDT', 'ETH/USDT:USDT']
        medium_volatility = ['BNB/USDT:USDT', 'ADA/USDT:USDT']
        
        if symbol in high_volatility:
            return 'high'
        elif symbol in medium_volatility:
            return 'medium'
        else:
            return 'low'
