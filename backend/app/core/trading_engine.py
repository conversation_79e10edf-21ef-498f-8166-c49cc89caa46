"""
Основной торговый движок
"""
import asyncio
from typing import Dict, List, Optional
from datetime import datetime
import structlog
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.exchange_manager import exchange_manager
from app.core.grid_algorithm import GridAlgorithm
from app.core.risk_manager import RiskManager
from app.models import Strategy, Order, Trade
from app.models.bot import GridBot
from app.models.enums import StrategyStatus
from app.core.database import AsyncSessionLocal
from app.database import get_db

logger = structlog.get_logger()


class TradingEngine:
    """Основной торговый движок"""
    
    def __init__(self):
        self.active_strategies: Dict[str, GridAlgorithm] = {}
        self.active_bots: Dict[int, 'GridBotAlgorithm'] = {}
        self.risk_manager = RiskManager()
        self.running = False
        self.monitor_task: Optional[asyncio.Task] = None
    
    async def start(self):
        """Запустить торговый движок"""
        if self.running:
            logger.warning("Торговый движок уже запущен")
            return
        
        self.running = True
        logger.info("Запуск торгового движка")
        
        # Восстановить активные стратегии из БД
        await self._restore_active_strategies()
        
        # Запустить мониторинг
        self.monitor_task = asyncio.create_task(self._monitor_strategies())
    
    async def stop(self):
        """Остановить торговый движок"""
        if not self.running:
            return
        
        logger.info("Остановка торгового движка")
        self.running = False
        
        # Остановить мониторинг
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        # Остановить все стратегии
        for strategy_id in list(self.active_strategies.keys()):
            await self.stop_strategy(strategy_id)
        
        # Отключиться от всех бирж
        await exchange_manager.disconnect_all()
    
    async def start_strategy(self, strategy_id: str) -> bool:
        """Запустить стратегию"""
        try:
            async with AsyncSessionLocal() as db:
                # Получить стратегию из БД
                strategy = await self._get_strategy(db, strategy_id)
                if not strategy:
                    logger.error(f"Стратегия {strategy_id} не найдена")
                    return False
                
                if strategy.status == StrategyStatus.ACTIVE:
                    logger.warning(f"Стратегия {strategy_id} уже активна")
                    return True
                
                # Проверить риски
                if not await self.risk_manager.validate_strategy(strategy):
                    logger.error(f"Стратегия {strategy_id} не прошла проверку рисков")
                    return False
                
                # Создать алгоритм сетки
                grid_algorithm = GridAlgorithm(strategy, exchange_manager)
                
                # Запустить алгоритм
                if await grid_algorithm.start():
                    self.active_strategies[strategy_id] = grid_algorithm
                    
                    # Обновить статус в БД
                    strategy.status = StrategyStatus.ACTIVE
                    strategy.is_active = True
                    strategy.started_at = datetime.utcnow()
                    await db.commit()
                    
                    logger.info(f"Стратегия {strategy_id} запущена")
                    return True
                else:
                    logger.error(f"Не удалось запустить стратегию {strategy_id}")
                    return False
                    
        except Exception as e:
            logger.error(f"Ошибка запуска стратегии {strategy_id}: {e}")
            return False
    
    async def stop_strategy(self, strategy_id: str) -> bool:
        """Остановить стратегию"""
        try:
            if strategy_id not in self.active_strategies:
                logger.warning(f"Стратегия {strategy_id} не активна")
                return True
            
            # Остановить алгоритм
            grid_algorithm = self.active_strategies[strategy_id]
            await grid_algorithm.stop()
            
            # Удалить из активных
            del self.active_strategies[strategy_id]
            
            # Обновить статус в БД
            async with AsyncSessionLocal() as db:
                strategy = await self._get_strategy(db, strategy_id)
                if strategy:
                    strategy.status = StrategyStatus.STOPPED
                    strategy.is_active = False
                    strategy.stopped_at = datetime.utcnow()
                    await db.commit()
            
            logger.info(f"Стратегия {strategy_id} остановлена")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка остановки стратегии {strategy_id}: {e}")
            return False
    
    async def pause_strategy(self, strategy_id: str) -> bool:
        """Приостановить стратегию"""
        try:
            if strategy_id not in self.active_strategies:
                logger.warning(f"Стратегия {strategy_id} не активна")
                return False
            
            grid_algorithm = self.active_strategies[strategy_id]
            await grid_algorithm.pause()
            
            # Обновить статус в БД
            async with AsyncSessionLocal() as db:
                strategy = await self._get_strategy(db, strategy_id)
                if strategy:
                    strategy.status = StrategyStatus.PAUSED
                    await db.commit()
            
            logger.info(f"Стратегия {strategy_id} приостановлена")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка приостановки стратегии {strategy_id}: {e}")
            return False
    
    async def resume_strategy(self, strategy_id: str) -> bool:
        """Возобновить стратегию"""
        try:
            if strategy_id not in self.active_strategies:
                logger.warning(f"Стратегия {strategy_id} не активна")
                return False
            
            grid_algorithm = self.active_strategies[strategy_id]
            await grid_algorithm.resume()
            
            # Обновить статус в БД
            async with AsyncSessionLocal() as db:
                strategy = await self._get_strategy(db, strategy_id)
                if strategy:
                    strategy.status = StrategyStatus.ACTIVE
                    await db.commit()
            
            logger.info(f"Стратегия {strategy_id} возобновлена")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка возобновления стратегии {strategy_id}: {e}")
            return False
    
    async def get_strategy_status(self, strategy_id: str) -> Optional[Dict]:
        """Получить статус стратегии"""
        if strategy_id in self.active_strategies:
            grid_algorithm = self.active_strategies[strategy_id]
            return await grid_algorithm.get_status()
        return None
    
    async def _monitor_strategies(self):
        """Мониторинг всех активных стратегий"""
        while self.running:
            try:
                for strategy_id, grid_algorithm in list(self.active_strategies.items()):
                    try:
                        # Проверить состояние стратегии
                        if not await grid_algorithm.is_healthy():
                            logger.warning(f"Стратегия {strategy_id} в нездоровом состоянии")
                            await self._handle_unhealthy_strategy(strategy_id)
                        
                        # Проверить риски
                        if not await self.risk_manager.monitor_strategy(grid_algorithm.strategy):
                            logger.warning(f"Стратегия {strategy_id} превысила лимиты рисков")
                            await self.stop_strategy(strategy_id)
                        
                    except Exception as e:
                        logger.error(f"Ошибка мониторинга стратегии {strategy_id}: {e}")
                
                await asyncio.sleep(5)  # Проверка каждые 5 секунд
                
            except Exception as e:
                logger.error(f"Ошибка в мониторинге стратегий: {e}")
                await asyncio.sleep(10)
    
    async def _restore_active_strategies(self):
        """Восстановить активные стратегии из БД"""
        try:
            async with AsyncSessionLocal() as db:
                # Найти все активные стратегии
                result = await db.execute(
                    "SELECT id FROM strategies WHERE status = 'active' AND is_active = true"
                )
                strategy_ids = [row[0] for row in result.fetchall()]
                
                for strategy_id in strategy_ids:
                    logger.info(f"Восстановление стратегии {strategy_id}")
                    await self.start_strategy(str(strategy_id))
                    
        except Exception as e:
            logger.error(f"Ошибка восстановления стратегий: {e}")
    
    async def _get_strategy(self, db: AsyncSession, strategy_id: str) -> Optional[Strategy]:
        """Получить стратегию из БД"""
        result = await db.execute(
            "SELECT * FROM strategies WHERE id = :id",
            {"id": strategy_id}
        )
        row = result.fetchone()
        if row:
            return Strategy(**dict(row))
        return None
    
    async def _handle_unhealthy_strategy(self, strategy_id: str):
        """Обработать нездоровую стратегию"""
        try:
            # Попытаться перезапустить
            await self.stop_strategy(strategy_id)
            await asyncio.sleep(5)
            await self.start_strategy(strategy_id)

        except Exception as e:
            logger.error(f"Не удалось восстановить стратегию {strategy_id}: {e}")

    # Методы для работы с ботами
    async def start_bot(self, bot_id: int) -> bool:
        """Запустить торгового бота"""
        try:
            from app.core.grid_bot_algorithm import GridBotAlgorithm
            from sqlalchemy.orm import Session
            from app.database import SessionLocal

            # Получить бота из БД
            with SessionLocal() as db:
                bot = db.query(GridBot).filter(GridBot.id == bot_id).first()
                if not bot:
                    logger.error(f"Бот {bot_id} не найден")
                    return False

                if bot.is_active:
                    logger.warning(f"Бот {bot_id} уже активен")
                    return True

                # Создать алгоритм для бота
                bot_algorithm = GridBotAlgorithm(bot)

                # Запустить алгоритм
                if await bot_algorithm.start():
                    self.active_bots[bot_id] = bot_algorithm
                    logger.info(f"Бот {bot_id} ({bot.name}) запущен")
                    return True
                else:
                    logger.error(f"Не удалось запустить бота {bot_id}")
                    return False

        except Exception as e:
            logger.error(f"Ошибка запуска бота {bot_id}: {e}")
            return False

    async def stop_bot(self, bot_id: int) -> bool:
        """Остановить торгового бота"""
        try:
            if bot_id not in self.active_bots:
                logger.warning(f"Бот {bot_id} не активен")
                return True

            # Остановить алгоритм
            bot_algorithm = self.active_bots[bot_id]
            await bot_algorithm.stop()

            # Удалить из активных
            del self.active_bots[bot_id]

            logger.info(f"Бот {bot_id} остановлен")
            return True

        except Exception as e:
            logger.error(f"Ошибка остановки бота {bot_id}: {e}")
            return False

    async def get_bot_status(self, bot_id: int) -> Optional[Dict]:
        """Получить статус бота"""
        if bot_id in self.active_bots:
            bot_algorithm = self.active_bots[bot_id]
            return await bot_algorithm.get_status()
        return None


# Глобальный экземпляр торгового движка
trading_engine = TradingEngine()
