"""
Алгоритм сеточной торговли для GridBot
"""
import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime
from decimal import Decimal
import structlog

from app.models.bot import GridBot
from app.models.order import Order, OrderType, OrderStatus
from app.database import SessionLocal

logger = structlog.get_logger()


class GridBotAlgorithm:
    """Алгоритм сеточной торговли для GridBot"""
    
    def __init__(self, bot: GridBot):
        self.bot = bot
        self.running = False
        self.monitor_task: Optional[asyncio.Task] = None
        self.grid_levels: List[Dict] = []
        self.current_price: Optional[float] = None
        
    async def start(self) -> bool:
        """Запустить алгоритм"""
        try:
            logger.info(f"Запуск алгоритма сетки для бота {self.bot.name}")
            
            # Создать уровни сетки
            await self._create_grid_levels()
            
            # Разместить начальные ордера (симуляция)
            await self._place_initial_orders()
            
            # Запустить мониторинг
            self.running = True
            self.monitor_task = asyncio.create_task(self._monitor_orders())
            
            logger.info(f"Алгоритм сетки запущен для {self.bot.symbol}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка запуска алгоритма для бота {self.bot.id}: {e}")
            return False
    
    async def stop(self) -> bool:
        """Остановить алгоритм"""
        try:
            logger.info(f"Остановка алгоритма для бота {self.bot.name}")
            
            self.running = False
            
            # Остановить мониторинг
            if self.monitor_task:
                self.monitor_task.cancel()
                try:
                    await self.monitor_task
                except asyncio.CancelledError:
                    pass
            
            # Отменить все активные ордера (симуляция)
            await self._cancel_all_orders()
            
            logger.info(f"Алгоритм остановлен для бота {self.bot.name}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка остановки алгоритма для бота {self.bot.id}: {e}")
            return False
    
    async def get_status(self) -> Dict:
        """Получить статус алгоритма"""
        with SessionLocal() as db:
            # Подсчитать статистику ордеров
            total_orders = db.query(Order).filter(Order.bot_id == self.bot.id).count()
            filled_orders = db.query(Order).filter(
                Order.bot_id == self.bot.id,
                Order.status == OrderStatus.FILLED
            ).count()
            
            return {
                "bot_id": self.bot.id,
                "name": self.bot.name,
                "symbol": self.bot.symbol,
                "running": self.running,
                "current_price": self.current_price,
                "grid_levels": len(self.grid_levels),
                "total_orders": total_orders,
                "filled_orders": filled_orders,
                "total_profit": float(self.bot.total_profit),
                "total_trades": self.bot.total_trades
            }
    
    async def _create_grid_levels(self):
        """Создать уровни сетки"""
        try:
            levels = self.bot.get_grid_levels()
            self.grid_levels = []
            
            for i, price in enumerate(levels):
                # Определяем тип ордера для каждого уровня
                if i < len(levels) // 2:
                    order_type = OrderType.BUY
                else:
                    order_type = OrderType.SELL
                
                level = {
                    "level": i,
                    "price": float(price),
                    "order_type": order_type,
                    "amount": float(self.bot.order_amount),
                    "is_filled": False,
                    "order_id": None
                }
                self.grid_levels.append(level)
            
            logger.info(f"Создано {len(self.grid_levels)} уровней сетки для бота {self.bot.name}")
            
        except Exception as e:
            logger.error(f"Ошибка создания уровней сетки: {e}")
            raise
    
    async def _place_initial_orders(self):
        """Разместить начальные ордера (симуляция)"""
        try:
            with SessionLocal() as db:
                # Симулируем размещение ордеров на каждом уровне
                for level in self.grid_levels:
                    order = Order(
                        bot_id=self.bot.id,
                        symbol=self.bot.symbol,
                        order_type=level["order_type"],
                        amount=Decimal(str(level["amount"])),
                        price=Decimal(str(level["price"])),
                        status=OrderStatus.PENDING
                    )
                    
                    db.add(order)
                    db.commit()
                    db.refresh(order)
                    
                    level["order_id"] = order.id
                    
                    logger.info(
                        f"Размещен ордер {order.id}: {level['order_type'].value} "
                        f"{level['amount']} {self.bot.symbol} по цене {level['price']}"
                    )
                
                logger.info(f"Размещено {len(self.grid_levels)} начальных ордеров")
                
        except Exception as e:
            logger.error(f"Ошибка размещения начальных ордеров: {e}")
            raise
    
    async def _monitor_orders(self):
        """Мониторинг ордеров"""
        while self.running:
            try:
                # Симулируем проверку ордеров
                await self._check_orders()
                
                # Симулируем обновление текущей цены
                await self._update_current_price()
                
                # Проверяем возможность исполнения ордеров
                await self._simulate_order_execution()
                
                await asyncio.sleep(5)  # Проверка каждые 5 секунд
                
            except Exception as e:
                logger.error(f"Ошибка в мониторинге ордеров: {e}")
                await asyncio.sleep(10)
    
    async def _check_orders(self):
        """Проверить статус ордеров"""
        try:
            with SessionLocal() as db:
                orders = db.query(Order).filter(
                    Order.bot_id == self.bot.id,
                    Order.status == OrderStatus.PENDING
                ).all()
                
                for order in orders:
                    # Здесь будет логика проверки статуса на бирже
                    # Пока что просто логируем
                    pass
                    
        except Exception as e:
            logger.error(f"Ошибка проверки ордеров: {e}")
    
    async def _update_current_price(self):
        """Обновить текущую цену (симуляция)"""
        try:
            # Симулируем получение цены
            # В реальной реализации здесь будет запрос к бирже
            import random
            
            if self.current_price is None:
                # Устанавливаем начальную цену в середине диапазона
                self.current_price = (float(self.bot.lower_price) + float(self.bot.upper_price)) / 2
            else:
                # Симулируем небольшие изменения цены
                change_percent = random.uniform(-0.5, 0.5)  # ±0.5%
                self.current_price *= (1 + change_percent / 100)
                
                # Ограничиваем цену диапазоном сетки
                self.current_price = max(
                    float(self.bot.lower_price),
                    min(float(self.bot.upper_price), self.current_price)
                )
                
        except Exception as e:
            logger.error(f"Ошибка обновления цены: {e}")
    
    async def _simulate_order_execution(self):
        """Симулировать исполнение ордеров"""
        try:
            if self.current_price is None:
                return
                
            with SessionLocal() as db:
                orders = db.query(Order).filter(
                    Order.bot_id == self.bot.id,
                    Order.status == OrderStatus.PENDING
                ).all()
                
                for order in orders:
                    order_price = float(order.price)
                    
                    # Проверяем условия исполнения
                    should_fill = False
                    
                    if order.order_type == OrderType.BUY and self.current_price <= order_price:
                        should_fill = True
                    elif order.order_type == OrderType.SELL and self.current_price >= order_price:
                        should_fill = True
                    
                    if should_fill:
                        # Исполняем ордер
                        order.status = OrderStatus.FILLED
                        order.filled_amount = order.amount
                        order.filled_price = Decimal(str(self.current_price))
                        order.filled_at = datetime.now()
                        
                        # Обновляем статистику бота
                        self.bot.total_trades += 1
                        
                        # Простой расчет прибыли (упрощенный)
                        if order.order_type == OrderType.SELL:
                            profit = float(order.amount) * (self.current_price - order_price) * 0.01
                            self.bot.total_profit += Decimal(str(profit))
                        
                        db.commit()
                        
                        logger.info(
                            f"Исполнен ордер {order.id}: {order.order_type.value} "
                            f"{order.amount} по цене {self.current_price}"
                        )
                        
                        # Создаем встречный ордер
                        await self._create_counter_order(order)
                
        except Exception as e:
            logger.error(f"Ошибка симуляции исполнения ордеров: {e}")
    
    async def _create_counter_order(self, filled_order: Order):
        """Создать встречный ордер после исполнения"""
        try:
            with SessionLocal() as db:
                # Определяем тип встречного ордера
                if filled_order.order_type == OrderType.BUY:
                    new_order_type = OrderType.SELL
                    new_price = float(filled_order.filled_price) * (1 + float(self.bot.profit_percentage) / 100)
                else:
                    new_order_type = OrderType.BUY
                    new_price = float(filled_order.filled_price) * (1 - float(self.bot.profit_percentage) / 100)
                
                # Создаем новый ордер
                new_order = Order(
                    bot_id=self.bot.id,
                    symbol=self.bot.symbol,
                    order_type=new_order_type,
                    amount=filled_order.amount,
                    price=Decimal(str(new_price)),
                    status=OrderStatus.PENDING,
                    parent_order_id=filled_order.id
                )
                
                db.add(new_order)
                db.commit()
                
                logger.info(
                    f"Создан встречный ордер {new_order.id}: {new_order_type.value} "
                    f"{new_order.amount} по цене {new_price}"
                )
                
        except Exception as e:
            logger.error(f"Ошибка создания встречного ордера: {e}")
    
    async def _cancel_all_orders(self):
        """Отменить все активные ордера"""
        try:
            with SessionLocal() as db:
                orders = db.query(Order).filter(
                    Order.bot_id == self.bot.id,
                    Order.status == OrderStatus.PENDING
                ).all()
                
                for order in orders:
                    order.status = OrderStatus.CANCELLED
                
                db.commit()
                
                logger.info(f"Отменено {len(orders)} ордеров для бота {self.bot.name}")
                
        except Exception as e:
            logger.error(f"Ошибка отмены ордеров: {e}")
