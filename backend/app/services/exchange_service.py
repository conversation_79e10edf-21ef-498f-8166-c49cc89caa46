"""
Сервис для работы с биржами через CCXT.
"""

import ccxt
import asyncio
from typing import Dict, Optional, List, Any
from datetime import datetime, timedelta
import structlog

from app.models.exchange_key import ExchangeKey, ExchangeType, NetworkType
from app.database import SessionLocal

logger = structlog.get_logger()


class ExchangeService:
    """Сервис для работы с биржами"""
    
    def __init__(self):
        self._exchange_cache: Dict[int, ccxt.Exchange] = {}
        self._cache_timeout = timedelta(minutes=30)
        self._last_cache_clear = datetime.now()
    
    def _get_exchange_class(self, exchange_type: ExchangeType) -> type:
        """Получить класс биржи CCXT"""
        exchange_classes = {
            ExchangeType.BINANCE: ccxt.binance,
            ExchangeType.BYBIT: ccxt.bybit,
            ExchangeType.OKX: ccxt.okx,
            ExchangeType.KUCOIN: ccxt.kucoin,
            ExchangeType.GATE: ccxt.gate,
            ExchangeType.HUOBI: ccxt.huobi,
            ExchangeType.BITGET: ccxt.bitget,
        }
        
        exchange_class = exchange_classes.get(exchange_type)
        if not exchange_class:
            raise ValueError(f"Неподдерживаемая биржа: {exchange_type}")
        
        return exchange_class
    
    def _create_exchange_instance(self, exchange_key: ExchangeKey) -> ccxt.Exchange:
        """Создать экземпляр биржи"""
        try:
            exchange_class = self._get_exchange_class(exchange_key.exchange)
            config = exchange_key.get_ccxt_config()
            
            # Создаем экземпляр биржи
            exchange = exchange_class(config)
            
            logger.info(
                f"Создан экземпляр биржи {exchange_key.exchange} "
                f"({exchange_key.network}) для ключа {exchange_key.id}"
            )
            
            return exchange
            
        except Exception as e:
            logger.error(f"Ошибка создания экземпляра биржи: {e}")
            raise
    
    def get_exchange(self, exchange_key_id: int) -> Optional[ccxt.Exchange]:
        """Получить экземпляр биржи из кэша или создать новый"""
        try:
            # Очищаем кэш если прошло много времени
            if datetime.now() - self._last_cache_clear > self._cache_timeout:
                self._exchange_cache.clear()
                self._last_cache_clear = datetime.now()
            
            # Проверяем кэш
            if exchange_key_id in self._exchange_cache:
                return self._exchange_cache[exchange_key_id]
            
            # Получаем ключ из БД
            with SessionLocal() as db:
                exchange_key = db.query(ExchangeKey).filter(
                    ExchangeKey.id == exchange_key_id,
                    ExchangeKey.is_active == True
                ).first()
                
                if not exchange_key:
                    logger.error(f"Ключ биржи {exchange_key_id} не найден или неактивен")
                    return None
                
                # Создаем экземпляр
                exchange = self._create_exchange_instance(exchange_key)
                
                # Кэшируем
                self._exchange_cache[exchange_key_id] = exchange
                
                return exchange
                
        except Exception as e:
            logger.error(f"Ошибка получения экземпляра биржи: {e}")
            return None
    
    async def validate_api_keys(self, exchange_key: ExchangeKey) -> Dict[str, Any]:
        """Валидация API ключей"""
        try:
            exchange = self._create_exchange_instance(exchange_key)
            
            # Загружаем рынки для проверки подключения
            await asyncio.get_event_loop().run_in_executor(
                None, exchange.load_markets
            )
            
            # Получаем баланс для проверки приватного API
            balance = await asyncio.get_event_loop().run_in_executor(
                None, exchange.fetch_balance
            )
            
            # Проверяем права доступа
            permissions = await self._check_permissions(exchange)
            
            result = {
                'valid': True,
                'balance': balance,
                'permissions': permissions,
                'exchange_info': {
                    'name': exchange.name,
                    'id': exchange.id,
                    'version': getattr(exchange, 'version', 'unknown'),
                    'has': {
                        'fetchBalance': exchange.has.get('fetchBalance', False),
                        'createOrder': exchange.has.get('createOrder', False),
                        'cancelOrder': exchange.has.get('cancelOrder', False),
                        'fetchOrders': exchange.has.get('fetchOrders', False),
                    }
                },
                'error': None
            }
            
            logger.info(f"API ключи валидны для {exchange_key.exchange} ({exchange_key.network})")
            return result
            
        except ccxt.AuthenticationError as e:
            logger.error(f"Ошибка аутентификации: {e}")
            return {
                'valid': False,
                'balance': {},
                'permissions': {},
                'exchange_info': {},
                'error': f"Ошибка аутентификации: {str(e)}"
            }
        except ccxt.PermissionDenied as e:
            logger.error(f"Недостаточно прав: {e}")
            return {
                'valid': False,
                'balance': {},
                'permissions': {},
                'exchange_info': {},
                'error': f"Недостаточно прав: {str(e)}"
            }
        except Exception as e:
            logger.error(f"Ошибка валидации API ключей: {e}")
            return {
                'valid': False,
                'balance': {},
                'permissions': {},
                'exchange_info': {},
                'error': f"Ошибка подключения: {str(e)}"
            }
    
    async def _check_permissions(self, exchange: ccxt.Exchange) -> Dict[str, bool]:
        """Проверить права доступа API ключа"""
        permissions = {
            'spot_trading': False,
            'futures_trading': False,
            'read_balance': False,
            'withdraw': False
        }
        
        try:
            # Проверяем чтение баланса
            await asyncio.get_event_loop().run_in_executor(
                None, exchange.fetch_balance
            )
            permissions['read_balance'] = True
            
            # Проверяем возможность создания ордеров (тестовый запрос)
            if exchange.has.get('createOrder'):
                permissions['spot_trading'] = True
            
            # Для фьючерсов проверяем отдельно
            if hasattr(exchange, 'set_sandbox_mode'):
                permissions['futures_trading'] = True
                
        except Exception as e:
            logger.warning(f"Не удалось проверить некоторые права: {e}")
        
        return permissions
    
    async def get_balance(self, exchange_key_id: int, force_refresh: bool = False) -> Dict[str, Any]:
        """Получить баланс с биржи"""
        try:
            with SessionLocal() as db:
                exchange_key = db.query(ExchangeKey).filter(
                    ExchangeKey.id == exchange_key_id
                ).first()
                
                if not exchange_key:
                    return {'error': 'Ключ не найден'}
                
                # Проверяем кэш
                if not force_refresh and exchange_key.balance_updated:
                    cache_age = datetime.now() - exchange_key.balance_updated
                    if cache_age < timedelta(minutes=5):  # Кэш на 5 минут
                        return {
                            'balance': exchange_key.balance,
                            'cached': True,
                            'updated_at': exchange_key.balance_updated
                        }
                
                # Получаем свежий баланс
                exchange = self.get_exchange(exchange_key_id)
                if not exchange:
                    return {'error': 'Не удалось подключиться к бирже'}
                
                balance = await asyncio.get_event_loop().run_in_executor(
                    None, exchange.fetch_balance
                )
                
                # Обновляем кэш
                exchange_key.balance = balance
                exchange_key.balance_updated = datetime.now()
                db.commit()
                
                return {
                    'balance': balance,
                    'cached': False,
                    'updated_at': exchange_key.balance_updated
                }
                
        except Exception as e:
            logger.error(f"Ошибка получения баланса: {e}")
            return {'error': str(e)}
    
    async def get_supported_exchanges(self) -> List[Dict[str, Any]]:
        """Получить список поддерживаемых бирж"""
        exchanges = []
        
        for exchange_type in ExchangeType:
            try:
                exchange_class = self._get_exchange_class(exchange_type)
                
                # Создаем временный экземпляр для получения информации
                temp_exchange = exchange_class({'enableRateLimit': True})
                
                exchanges.append({
                    'id': exchange_type.value,
                    'name': temp_exchange.name,
                    'countries': getattr(temp_exchange, 'countries', []),
                    'urls': getattr(temp_exchange, 'urls', {}),
                    'has': {
                        'spot': temp_exchange.has.get('spot', True),
                        'future': temp_exchange.has.get('future', False),
                        'fetchBalance': temp_exchange.has.get('fetchBalance', False),
                        'createOrder': temp_exchange.has.get('createOrder', False),
                    },
                    'requires_passphrase': exchange_type in [ExchangeType.OKX, ExchangeType.KUCOIN]
                })
                
            except Exception as e:
                logger.warning(f"Не удалось получить информацию о бирже {exchange_type}: {e}")
        
        return exchanges
    
    def clear_cache(self, exchange_key_id: Optional[int] = None):
        """Очистить кэш биржи"""
        if exchange_key_id:
            self._exchange_cache.pop(exchange_key_id, None)
        else:
            self._exchange_cache.clear()
        
        logger.info(f"Кэш биржи очищен {'для ключа ' + str(exchange_key_id) if exchange_key_id else 'полностью'}")


# Глобальный экземпляр сервиса
exchange_service = ExchangeService()
