"""
Сервис для работы с пользователями.
"""

from datetime import datetime, timedelta
from typing import Optional, List, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_
from fastapi import HTTPException, status
import secrets
import jwt
from datetime import datetime, timedelta

from app.models.user import User, UserRole, UserStatus
from app.schemas.user import (
    UserCreate, UserUpdate, UserPasswordUpdate, 
    AdminUserUpdate, UserLogin
)


class UserService:
    """Сервис для работы с пользователями"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.secret_key = "earnlyze-secret-key-2024"  # В продакшене из переменных окружения
        self.algorithm = "HS256"
        self.access_token_expire_minutes = 30
    
    async def create_user(self, user_data: UserCreate) -> User:
        """Создать нового пользователя"""
        # Проверить уникальность username и email
        existing_user = await self.db.execute(
            select(User).where(
                or_(User.username == user_data.username, User.email == user_data.email)
            )
        )
        if existing_user.scalar_one_or_none():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Пользователь с таким именем или email уже существует"
            )
        
        # Создать пользователя
        user = User(
            username=user_data.username,
            email=user_data.email,
            full_name=user_data.full_name,
            status=UserStatus.PENDING
        )
        user.set_password(user_data.password)
        
        # Сгенерировать токен подтверждения email
        verification_token = user.generate_email_verification_token()
        
        self.db.add(user)
        await self.db.commit()
        await self.db.refresh(user)
        
        # TODO: Отправить email с подтверждением
        # await self.send_verification_email(user.email, verification_token)
        
        return user
    
    async def authenticate_user(self, login_data: UserLogin) -> Optional[User]:
        """Аутентификация пользователя"""
        # Найти пользователя по username или email
        user = await self.db.execute(
            select(User).where(
                or_(User.username == login_data.username, User.email == login_data.username)
            )
        )
        user = user.scalar_one_or_none()
        
        if not user or not user.verify_password(login_data.password):
            return None
        
        # Проверить статус пользователя
        if user.status != UserStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Аккаунт не активен"
            )
        
        # Проверить OTP если включен
        if user.is_otp_enabled:
            if not login_data.otp_token:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Требуется OTP токен"
                )
            
            # Проверить OTP токен или резервный код
            if not (user.verify_otp(login_data.otp_token) or 
                    user.verify_backup_code(login_data.otp_token)):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Неверный OTP токен"
                )
        
        # Обновить время последнего входа
        user.last_login = datetime.utcnow()
        await self.db.commit()
        
        return user
    
    async def create_access_token(self, user: User) -> str:
        """Создать JWT токен"""
        expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        to_encode = {
            "sub": str(user.id),
            "username": user.username,
            "email": user.email,
            "role": user.role.value,
            "exp": expire
        }
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    async def verify_token(self, token: str) -> Optional[User]:
        """Проверить JWT токен"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            user_id = payload.get("sub")
            if user_id is None:
                return None
        except jwt.PyJWTError:
            return None
        
        user = await self.get_user_by_id(int(user_id))
        return user
    
    async def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Получить пользователя по ID"""
        result = await self.db.execute(select(User).where(User.id == user_id))
        return result.scalar_one_or_none()
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Получить пользователя по username"""
        result = await self.db.execute(select(User).where(User.username == username))
        return result.scalar_one_or_none()
    
    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Получить пользователя по email"""
        result = await self.db.execute(select(User).where(User.email == email))
        return result.scalar_one_or_none()
    
    async def update_user(self, user_id: int, user_data: UserUpdate) -> Optional[User]:
        """Обновить данные пользователя"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return None
        
        # Проверить уникальность при изменении username или email
        if user_data.username and user_data.username != user.username:
            existing = await self.get_user_by_username(user_data.username)
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Пользователь с таким именем уже существует"
                )
        
        if user_data.email and user_data.email != user.email:
            existing = await self.get_user_by_email(user_data.email)
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Пользователь с таким email уже существует"
                )
            # При смене email нужно повторное подтверждение
            user.is_email_verified = False
            user.generate_email_verification_token()
        
        # Обновить поля
        for field, value in user_data.dict(exclude_unset=True).items():
            setattr(user, field, value)
        
        await self.db.commit()
        await self.db.refresh(user)
        return user
    
    async def change_password(self, user_id: int, password_data: UserPasswordUpdate) -> bool:
        """Сменить пароль пользователя"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        
        # Проверить текущий пароль
        if not user.verify_password(password_data.current_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Неверный текущий пароль"
            )
        
        # Установить новый пароль
        user.set_password(password_data.new_password)
        await self.db.commit()
        return True
    
    async def verify_email(self, token: str) -> bool:
        """Подтвердить email по токену"""
        result = await self.db.execute(
            select(User).where(User.email_verification_token == token)
        )
        user = result.scalar_one_or_none()
        
        if not user:
            return False
        
        user.is_email_verified = True
        user.email_verified_at = datetime.utcnow()
        user.email_verification_token = None
        
        # Если это первое подтверждение, активировать аккаунт
        if user.status == UserStatus.PENDING:
            user.status = UserStatus.ACTIVE
        
        await self.db.commit()
        return True
    
    async def setup_otp(self, user_id: int) -> Tuple[str, str, List[str]]:
        """Настроить OTP для пользователя"""
        user = await self.get_user_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Пользователь не найден"
            )
        
        secret, qr_code = user.setup_otp()
        backup_codes = user.generate_backup_codes()
        
        await self.db.commit()
        return secret, qr_code, backup_codes
    
    async def enable_otp(self, user_id: int, token: str) -> bool:
        """Включить OTP после проверки токена"""
        user = await self.get_user_by_id(user_id)
        if not user or not user.otp_secret:
            return False
        
        if not user.verify_otp(token):
            return False
        
        user.is_otp_enabled = True
        await self.db.commit()
        return True
    
    async def disable_otp(self, user_id: int, password: str, token: Optional[str] = None) -> bool:
        """Отключить OTP"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        
        # Проверить пароль
        if not user.verify_password(password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Неверный пароль"
            )
        
        # Проверить OTP токен или резервный код если OTP включен
        if user.is_otp_enabled and token:
            if not (user.verify_otp(token) or user.verify_backup_code(token)):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Неверный OTP токен"
                )
        
        user.is_otp_enabled = False
        user.otp_secret = None
        user.backup_codes = None
        
        await self.db.commit()
        return True
    
    async def get_users_list(self, page: int = 1, per_page: int = 20) -> Tuple[List[User], int]:
        """Получить список пользователей (для админа)"""
        offset = (page - 1) * per_page
        
        # Получить пользователей
        result = await self.db.execute(
            select(User)
            .offset(offset)
            .limit(per_page)
            .order_by(User.created_at.desc())
        )
        users = result.scalars().all()
        
        # Получить общее количество
        count_result = await self.db.execute(select(func.count(User.id)))
        total = count_result.scalar()
        
        return list(users), total
    
    async def admin_update_user(self, user_id: int, user_data: AdminUserUpdate) -> Optional[User]:
        """Обновить пользователя (для админа)"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return None
        
        # Обновить поля
        for field, value in user_data.dict(exclude_unset=True).items():
            setattr(user, field, value)
        
        await self.db.commit()
        await self.db.refresh(user)
        return user
    
    async def delete_user(self, user_id: int) -> bool:
        """Удалить пользователя"""
        user = await self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.status = UserStatus.DELETED
        await self.db.commit()
        return True
