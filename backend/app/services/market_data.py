"""
Сервис для получения и обработки рыночных данных.
"""

import logging
import ccxt
from typing import Dict, Optional, List
from decimal import Decimal

from app.core.config import settings

logger = logging.getLogger(__name__)


class MarketDataService:
    """
    Сервис для работы с рыночными данными через CCXT.
    """
    
    def __init__(self):
        """
        Инициализация сервиса с подключением к бирже.
        """
        self.exchange = None
        self._initialize_exchange()
    
    def _initialize_exchange(self):
        """
        Инициализация подключения к бирже.
        """
        try:
            # Используем Binance как основную биржу
            self.exchange = ccxt.binance({
                'apiKey': settings.BINANCE_API_KEY,
                'secret': settings.BINANCE_SECRET_KEY,
                'sandbox': settings.BINANCE_TESTNET,  # Используем тестовую сеть
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'future'  # Используем фьючерсы
                }
            })
            
            # Загружаем рынки
            self.exchange.load_markets()
            logger.info("Подключение к бирже Binance установлено")
            
        except Exception as e:
            logger.error(f"Ошибка при подключении к бирже: {str(e)}")
            self.exchange = None
    
    def get_ticker(self, symbol: str) -> Optional[Dict]:
        """
        Получает текущую цену для торговой пары.
        
        Args:
            symbol: Торговая пара (например, 'BTC/USDT')
            
        Returns:
            Словарь с данными о цене или None при ошибке
        """
        if not self.exchange:
            logger.error("Биржа не инициализирована")
            return None
        
        try:
            ticker = self.exchange.fetch_ticker(symbol)
            return {
                'symbol': symbol,
                'price': Decimal(str(ticker['last'])),
                'bid': Decimal(str(ticker['bid'])) if ticker['bid'] else None,
                'ask': Decimal(str(ticker['ask'])) if ticker['ask'] else None,
                'volume': Decimal(str(ticker['baseVolume'])) if ticker['baseVolume'] else None,
                'timestamp': ticker['timestamp']
            }
        except Exception as e:
            logger.error(f"Ошибка при получении тикера для {symbol}: {str(e)}")
            return None
    
    def get_orderbook(self, symbol: str, limit: int = 20) -> Optional[Dict]:
        """
        Получает стакан заявок для торговой пары.
        
        Args:
            symbol: Торговая пара
            limit: Количество уровней в стакане
            
        Returns:
            Словарь со стаканом заявок или None при ошибке
        """
        if not self.exchange:
            logger.error("Биржа не инициализирована")
            return None
        
        try:
            orderbook = self.exchange.fetch_order_book(symbol, limit)
            return {
                'symbol': symbol,
                'bids': [[Decimal(str(price)), Decimal(str(amount))] for price, amount in orderbook['bids']],
                'asks': [[Decimal(str(price)), Decimal(str(amount))] for price, amount in orderbook['asks']],
                'timestamp': orderbook['timestamp']
            }
        except Exception as e:
            logger.error(f"Ошибка при получении стакана для {symbol}: {str(e)}")
            return None
    
    def get_balance(self) -> Optional[Dict]:
        """
        Получает баланс аккаунта.
        
        Returns:
            Словарь с балансами или None при ошибке
        """
        if not self.exchange:
            logger.error("Биржа не инициализирована")
            return None
        
        try:
            balance = self.exchange.fetch_balance()
            return {
                asset: {
                    'free': Decimal(str(info['free'])),
                    'used': Decimal(str(info['used'])),
                    'total': Decimal(str(info['total']))
                }
                for asset, info in balance.items()
                if isinstance(info, dict) and 'free' in info
            }
        except Exception as e:
            logger.error(f"Ошибка при получении баланса: {str(e)}")
            return None
    
    def update_pair_data(self, symbol: str) -> bool:
        """
        Обновляет данные для торговой пары.
        
        Args:
            symbol: Торговая пара
            
        Returns:
            True если обновление прошло успешно, False иначе
        """
        try:
            ticker = self.get_ticker(symbol)
            if ticker:
                logger.info(f"Данные для {symbol} обновлены: цена {ticker['price']}")
                return True
            return False
        except Exception as e:
            logger.error(f"Ошибка при обновлении данных для {symbol}: {str(e)}")
            return False
    
    def get_available_symbols(self) -> List[str]:
        """
        Получает список доступных торговых пар.
        
        Returns:
            Список торговых пар
        """
        if not self.exchange:
            return []
        
        try:
            markets = self.exchange.markets
            return [symbol for symbol in markets.keys() if '/USDT' in symbol]
        except Exception as e:
            logger.error(f"Ошибка при получении списка символов: {str(e)}")
            return []
