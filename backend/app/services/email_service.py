"""
Сервис для отправки email уведомлений.
"""

import os
import datetime
import aiosmtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional
import logging

logger = logging.getLogger(__name__)


class EmailService:
    """Сервис для отправки email"""
    
    def __init__(self):
        self.smtp_host = os.getenv("SMTP_HOST", "smtp.gmail.com")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.smtp_username = os.getenv("SMTP_USERNAME", "")
        self.smtp_password = os.getenv("SMTP_PASSWORD", "")
        self.from_email = os.getenv("FROM_EMAIL", "<EMAIL>")
        self.from_name = os.getenv("FROM_NAME", "Earnlyze")
        
        # Базовый URL приложения
        self.base_url = os.getenv("BASE_URL", "http://localhost:3000")
    
    async def send_email(
        self, 
        to_email: str, 
        subject: str, 
        html_content: str, 
        text_content: Optional[str] = None
    ) -> bool:
        """Отправить email"""
        try:
            # Создать сообщение
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = f"{self.from_name} <{self.from_email}>"
            message["To"] = to_email
            
            # Добавить текстовую версию
            if text_content:
                text_part = MIMEText(text_content, "plain", "utf-8")
                message.attach(text_part)
            
            # Добавить HTML версию
            html_part = MIMEText(html_content, "html", "utf-8")
            message.attach(html_part)
            
            # Отправить email
            await aiosmtplib.send(
                message,
                hostname=self.smtp_host,
                port=self.smtp_port,
                start_tls=True,
                username=self.smtp_username,
                password=self.smtp_password,
            )
            
            logger.info(f"Email отправлен на {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка отправки email на {to_email}: {e}")
            return False
    
    async def send_verification_email(self, email: str, token: str, username: str) -> bool:
        """Отправить email с подтверждением регистрации"""
        verification_url = f"{self.base_url}/verify-email?token={token}"
        
        subject = "Подтверждение регистрации в Earnlyze"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Подтверждение регистрации</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #7C3AED 0%, #A855F7 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                .content {{ background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }}
                .button {{ display: inline-block; background: #7C3AED; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Добро пожаловать в Earnlyze!</h1>
                </div>
                <div class="content">
                    <h2>Привет, {username}!</h2>
                    <p>Спасибо за регистрацию в Earnlyze - платформе для автоматизированной торговли криптовалютами.</p>
                    <p>Для завершения регистрации, пожалуйста, подтвердите ваш email адрес, нажав на кнопку ниже:</p>
                    <p style="text-align: center;">
                        <a href="{verification_url}" class="button">Подтвердить Email</a>
                    </p>
                    <p>Если кнопка не работает, скопируйте и вставьте эту ссылку в браузер:</p>
                    <p style="word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 5px;">
                        {verification_url}
                    </p>
                    <p><strong>Важно:</strong> Эта ссылка действительна в течение 24 часов.</p>
                </div>
                <div class="footer">
                    <p>Если вы не регистрировались в Earnlyze, просто проигнорируйте это письмо.</p>
                    <p>&copy; 2024 Earnlyze. Все права защищены.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        Добро пожаловать в Earnlyze!
        
        Привет, {username}!
        
        Спасибо за регистрацию в Earnlyze - платформе для автоматизированной торговли криптовалютами.
        
        Для завершения регистрации, пожалуйста, подтвердите ваш email адрес, перейдя по ссылке:
        {verification_url}
        
        Важно: Эта ссылка действительна в течение 24 часов.
        
        Если вы не регистрировались в Earnlyze, просто проигнорируйте это письмо.
        
        © 2024 Earnlyze. Все права защищены.
        """
        
        return await self.send_email(email, subject, html_content, text_content)
    
    async def send_password_reset_email(self, email: str, token: str, username: str) -> bool:
        """Отправить email для сброса пароля"""
        reset_url = f"{self.base_url}/reset-password?token={token}"
        
        subject = "Сброс пароля в Earnlyze"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Сброс пароля</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #7C3AED 0%, #A855F7 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                .content {{ background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }}
                .button {{ display: inline-block; background: #7C3AED; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
                .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Сброс пароля</h1>
                </div>
                <div class="content">
                    <h2>Привет, {username}!</h2>
                    <p>Мы получили запрос на сброс пароля для вашего аккаунта в Earnlyze.</p>
                    <p>Для создания нового пароля нажмите на кнопку ниже:</p>
                    <p style="text-align: center;">
                        <a href="{reset_url}" class="button">Сбросить пароль</a>
                    </p>
                    <p>Если кнопка не работает, скопируйте и вставьте эту ссылку в браузер:</p>
                    <p style="word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 5px;">
                        {reset_url}
                    </p>
                    <div class="warning">
                        <strong>Важно:</strong> Эта ссылка действительна в течение 1 часа. Если вы не запрашивали сброс пароля, просто проигнорируйте это письмо.
                    </div>
                </div>
                <div class="footer">
                    <p>Если у вас есть вопросы, свяжитесь с нашей поддержкой.</p>
                    <p>&copy; 2024 Earnlyze. Все права защищены.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        Сброс пароля в Earnlyze
        
        Привет, {username}!
        
        Мы получили запрос на сброс пароля для вашего аккаунта в Earnlyze.
        
        Для создания нового пароля перейдите по ссылке:
        {reset_url}
        
        Важно: Эта ссылка действительна в течение 1 часа. Если вы не запрашивали сброс пароля, просто проигнорируйте это письмо.
        
        Если у вас есть вопросы, свяжитесь с нашей поддержкой.
        
        © 2024 Earnlyze. Все права защищены.
        """
        
        return await self.send_email(email, subject, html_content, text_content)
    
    async def send_login_notification(self, email: str, username: str, ip_address: str) -> bool:
        """Отправить уведомление о входе в аккаунт"""
        subject = "Новый вход в ваш аккаунт Earnlyze"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Уведомление о входе</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #7C3AED 0%, #A855F7 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                .content {{ background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }}
                .info {{ background: #e3f2fd; border: 1px solid #90caf9; padding: 15px; border-radius: 5px; margin: 20px 0; }}
                .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 14px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Уведомление о входе</h1>
                </div>
                <div class="content">
                    <h2>Привет, {username}!</h2>
                    <p>Мы зафиксировали новый вход в ваш аккаунт Earnlyze.</p>
                    <div class="info">
                        <strong>Детали входа:</strong><br>
                        IP адрес: {ip_address}<br>
                        Время: {datetime.datetime.now().strftime('%d.%m.%Y %H:%M:%S')} UTC
                    </div>
                    <p>Если это были вы, никаких действий не требуется.</p>
                    <p>Если вы не входили в аккаунт, немедленно смените пароль и включите двухфакторную аутентификацию.</p>
                </div>
                <div class="footer">
                    <p>Если у вас есть вопросы, свяжитесь с нашей поддержкой.</p>
                    <p>&copy; 2024 Earnlyze. Все права защищены.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return await self.send_email(email, subject, html_content)
