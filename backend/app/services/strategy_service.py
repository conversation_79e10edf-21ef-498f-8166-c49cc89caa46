"""
Сервис для работы со стратегиями
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload
import structlog
from uuid import UUID

from app.models import Strategy, User
from app.schemas.strategy import StrategyCreate, StrategyUpdate
from app.models.enums import StrategyStatus

logger = structlog.get_logger()


class StrategyService:
    """Сервис для работы со стратегиями"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def create_strategy(self, strategy_data: StrategyCreate, user_id: Optional[UUID] = None) -> Strategy:
        """Создать новую стратегию"""
        try:
            # Если user_id не передан, используем тестового пользователя
            if not user_id:
                user_id = await self._get_or_create_test_user()
            
            strategy = Strategy(
                user_id=user_id,
                **strategy_data.dict()
            )
            
            self.db.add(strategy)
            await self.db.commit()
            await self.db.refresh(strategy)
            
            logger.info(f"Создана стратегия: {strategy.name} (ID: {strategy.id})")
            return strategy
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Ошибка создания стратегии: {e}")
            raise
    
    async def get_strategy(self, strategy_id: str) -> Optional[Strategy]:
        """Получить стратегию по ID"""
        try:
            result = await self.db.execute(
                select(Strategy)
                .options(selectinload(Strategy.orders), selectinload(Strategy.trades))
                .where(Strategy.id == UUID(strategy_id))
            )
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"Ошибка получения стратегии {strategy_id}: {e}")
            raise
    
    async def get_strategies(
        self,
        user_id: Optional[UUID] = None,
        skip: int = 0,
        limit: int = 100,
        status: Optional[StrategyStatus] = None
    ) -> List[Strategy]:
        """Получить список стратегий"""
        try:
            query = select(Strategy).offset(skip).limit(limit)
            
            if user_id:
                query = query.where(Strategy.user_id == user_id)
            
            if status:
                query = query.where(Strategy.status == status)
            
            result = await self.db.execute(query)
            return result.scalars().all()
            
        except Exception as e:
            logger.error(f"Ошибка получения стратегий: {e}")
            raise
    
    async def update_strategy(self, strategy_id: str, strategy_data: StrategyUpdate) -> Optional[Strategy]:
        """Обновить стратегию"""
        try:
            # Получить стратегию
            strategy = await self.get_strategy(strategy_id)
            if not strategy:
                return None
            
            # Проверить, что стратегия не активна (для изменения критических параметров)
            if strategy.is_active and self._has_critical_updates(strategy_data):
                raise ValueError("Нельзя изменять критические параметры активной стратегии")
            
            # Обновить поля
            update_data = strategy_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(strategy, field, value)
            
            await self.db.commit()
            await self.db.refresh(strategy)
            
            logger.info(f"Обновлена стратегия: {strategy_id}")
            return strategy
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Ошибка обновления стратегии {strategy_id}: {e}")
            raise
    
    async def delete_strategy(self, strategy_id: str) -> bool:
        """Удалить стратегию"""
        try:
            strategy = await self.get_strategy(strategy_id)
            if not strategy:
                return False
            
            # Проверить, что стратегия не активна
            if strategy.is_active:
                raise ValueError("Нельзя удалить активную стратегию")
            
            await self.db.delete(strategy)
            await self.db.commit()
            
            logger.info(f"Удалена стратегия: {strategy_id}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Ошибка удаления стратегии {strategy_id}: {e}")
            raise
    
    async def get_active_strategies(self, user_id: Optional[UUID] = None) -> List[Strategy]:
        """Получить активные стратегии"""
        return await self.get_strategies(
            user_id=user_id,
            status=StrategyStatus.ACTIVE
        )
    
    async def update_strategy_stats(
        self,
        strategy_id: str,
        total_profit: Optional[float] = None,
        total_trades: Optional[int] = None,
        current_drawdown: Optional[float] = None,
        current_position_size: Optional[float] = None,
        current_position_value: Optional[float] = None,
        average_entry_price: Optional[float] = None
    ) -> bool:
        """Обновить статистику стратегии"""
        try:
            update_data = {}
            
            if total_profit is not None:
                update_data['total_profit'] = total_profit
            if total_trades is not None:
                update_data['total_trades'] = total_trades
            if current_drawdown is not None:
                update_data['current_drawdown'] = current_drawdown
            if current_position_size is not None:
                update_data['current_position_size'] = current_position_size
            if current_position_value is not None:
                update_data['current_position_value'] = current_position_value
            if average_entry_price is not None:
                update_data['average_entry_price'] = average_entry_price
            
            if update_data:
                await self.db.execute(
                    update(Strategy)
                    .where(Strategy.id == UUID(strategy_id))
                    .values(**update_data)
                )
                await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Ошибка обновления статистики стратегии {strategy_id}: {e}")
            return False
    
    async def update_strategy_status(self, strategy_id: str, status: StrategyStatus, is_active: bool = None) -> bool:
        """Обновить статус стратегии"""
        try:
            update_data = {'status': status}
            
            if is_active is not None:
                update_data['is_active'] = is_active
            
            await self.db.execute(
                update(Strategy)
                .where(Strategy.id == UUID(strategy_id))
                .values(**update_data)
            )
            await self.db.commit()
            
            logger.info(f"Обновлен статус стратегии {strategy_id}: {status}")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Ошибка обновления статуса стратегии {strategy_id}: {e}")
            return False
    
    def _has_critical_updates(self, strategy_data: StrategyUpdate) -> bool:
        """Проверить, есть ли критические обновления"""
        critical_fields = [
            'exchange', 'symbol', 'direction', 'grid_count',
            'grid_step_percent', 'leverage', 'margin_mode'
        ]
        
        update_data = strategy_data.dict(exclude_unset=True)
        return any(field in update_data for field in critical_fields)
    
    async def _get_or_create_test_user(self) -> UUID:
        """Получить или создать тестового пользователя"""
        try:
            # Попытаться найти тестового пользователя
            result = await self.db.execute(
                select(User).where(User.username == "test_user")
            )
            user = result.scalar_one_or_none()
            
            if not user:
                # Создать тестового пользователя
                user = User(
                    username="test_user",
                    email="<EMAIL>",
                    hashed_password="test_password_hash"
                )
                self.db.add(user)
                await self.db.commit()
                await self.db.refresh(user)
                logger.info("Создан тестовый пользователь")
            
            return user.id
            
        except Exception as e:
            logger.error(f"Ошибка создания тестового пользователя: {e}")
            raise
