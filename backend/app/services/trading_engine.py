"""
Торговый движок для выполнения сеточной торговли.
"""

import logging
from typing import List, Optional, Dict
from decimal import Decimal
from sqlalchemy.orm import Session

from app.models.bot import GridBot
from app.models.order import Order, OrderStatus, OrderType
from app.services.market_data import MarketDataService
from app.database import SessionLocal

logger = logging.getLogger(__name__)


class TradingEngine:
    """
    Основной торговый движок для сеточных ботов.
    """
    
    def __init__(self, bot: GridBot):
        """
        Инициализация торгового движка для конкретного бота.
        
        Args:
            bot: Экземпляр сеточного бота
        """
        self.bot = bot
        self.market_service = MarketDataService()
        self.db = SessionLocal()
    
    def __del__(self):
        """
        Закрытие соединения с базой данных при удалении объекта.
        """
        if hasattr(self, 'db'):
            self.db.close()
    
    def process_bot(self) -> bool:
        """
        Основная логика обработки бота.
        
        Returns:
            True если обработка прошла успешно, False иначе
        """
        try:
            if not self.bot.is_active:
                logger.info(f"Бот {self.bot.name} неактивен")
                return True
            
            # Получаем текущую цену
            ticker = self.market_service.get_ticker(self.bot.symbol)
            if not ticker:
                logger.error(f"Не удалось получить цену для {self.bot.symbol}")
                return False
            
            current_price = ticker['price']
            logger.info(f"Текущая цена {self.bot.symbol}: {current_price}")
            
            # Проверяем и создаем ордера сетки
            self._check_and_create_grid_orders(current_price)
            
            # Проверяем исполненные ордера
            self._check_filled_orders()
            
            return True
            
        except Exception as e:
            logger.error(f"Ошибка при обработке бота {self.bot.name}: {str(e)}")
            return False
    
    def _check_and_create_grid_orders(self, current_price: Decimal):
        """
        Проверяет и создает ордера сетки.
        
        Args:
            current_price: Текущая цена
        """
        try:
            # Получаем активные ордера
            active_orders = self.db.query(Order).filter(
                Order.bot_id == self.bot.id,
                Order.status == OrderStatus.PENDING
            ).all()
            
            # Рассчитываем уровни сетки
            grid_levels = self._calculate_grid_levels(current_price)
            
            # Создаем недостающие ордера
            for level in grid_levels:
                # Проверяем, есть ли уже ордер на этом уровне
                existing_order = any(
                    abs(float(order.price) - float(level['price'])) < 0.01
                    for order in active_orders
                )
                
                if not existing_order:
                    self._create_grid_order(level)
            
        except Exception as e:
            logger.error(f"Ошибка при создании ордеров сетки: {str(e)}")
    
    def _calculate_grid_levels(self, current_price: Decimal) -> List[Dict]:
        """
        Рассчитывает уровни сетки.
        
        Args:
            current_price: Текущая цена
            
        Returns:
            Список уровней сетки
        """
        levels = []
        
        # Рассчитываем размер шага сетки
        price_range = self.bot.upper_price - self.bot.lower_price
        step_size = price_range / self.bot.grid_count
        
        # Создаем уровни покупки (ниже текущей цены)
        for i in range(1, self.bot.grid_count // 2 + 1):
            buy_price = current_price - (step_size * i)
            if buy_price >= self.bot.lower_price:
                levels.append({
                    'price': buy_price,
                    'type': OrderType.BUY,
                    'amount': self.bot.order_amount
                })
        
        # Создаем уровни продажи (выше текущей цены)
        for i in range(1, self.bot.grid_count // 2 + 1):
            sell_price = current_price + (step_size * i)
            if sell_price <= self.bot.upper_price:
                levels.append({
                    'price': sell_price,
                    'type': OrderType.SELL,
                    'amount': self.bot.order_amount
                })
        
        return levels
    
    def _create_grid_order(self, level: Dict):
        """
        Создает ордер сетки.
        
        Args:
            level: Уровень сетки с параметрами ордера
        """
        try:
            order = Order(
                bot_id=self.bot.id,
                symbol=self.bot.symbol,
                order_type=level['type'],
                amount=level['amount'],
                price=level['price'],
                status=OrderStatus.PENDING
            )
            
            self.db.add(order)
            self.db.commit()
            
            logger.info(f"Создан ордер {level['type'].value} {level['amount']} {self.bot.symbol} по цене {level['price']}")
            
        except Exception as e:
            logger.error(f"Ошибка при создании ордера: {str(e)}")
            self.db.rollback()
    
    def _check_filled_orders(self):
        """
        Проверяет исполненные ордера и создает встречные.
        """
        try:
            # Получаем исполненные ордера
            filled_orders = self.db.query(Order).filter(
                Order.bot_id == self.bot.id,
                Order.status == OrderStatus.FILLED
            ).all()
            
            for order in filled_orders:
                # Создаем встречный ордер
                self._create_counter_order(order)
                
                # Обновляем статус обработанного ордера
                order.status = OrderStatus.COMPLETED
                self.db.commit()
            
        except Exception as e:
            logger.error(f"Ошибка при проверке исполненных ордеров: {str(e)}")
            self.db.rollback()
    
    def _create_counter_order(self, filled_order: Order):
        """
        Создает встречный ордер для исполненного.
        
        Args:
            filled_order: Исполненный ордер
        """
        try:
            # Определяем тип встречного ордера
            counter_type = OrderType.SELL if filled_order.order_type == OrderType.BUY else OrderType.BUY
            
            # Рассчитываем цену встречного ордера
            if counter_type == OrderType.SELL:
                counter_price = filled_order.price * (1 + self.bot.profit_percentage / 100)
            else:
                counter_price = filled_order.price * (1 - self.bot.profit_percentage / 100)
            
            # Создаем встречный ордер
            counter_order = Order(
                bot_id=self.bot.id,
                symbol=self.bot.symbol,
                order_type=counter_type,
                amount=filled_order.amount,
                price=counter_price,
                status=OrderStatus.PENDING,
                parent_order_id=filled_order.id
            )
            
            self.db.add(counter_order)
            self.db.commit()
            
            logger.info(f"Создан встречный ордер {counter_type.value} по цене {counter_price}")
            
        except Exception as e:
            logger.error(f"Ошибка при создании встречного ордера: {str(e)}")
            self.db.rollback()
    
    def execute_order(self, order: Order) -> bool:
        """
        Выполняет конкретный ордер.
        
        Args:
            order: Ордер для выполнения
            
        Returns:
            True если ордер выполнен успешно, False иначе
        """
        try:
            # Здесь должна быть логика отправки ордера на биржу
            # Пока что просто помечаем как исполненный
            order.status = OrderStatus.FILLED
            self.db.commit()
            
            logger.info(f"Ордер {order.id} выполнен")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка при выполнении ордера {order.id}: {str(e)}")
            self.db.rollback()
            return False
