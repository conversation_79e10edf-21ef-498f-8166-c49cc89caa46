"""
Сервис для Telegram уведомлений
"""
from typing import Optional
import structlog
from aiogram import <PERSON><PERSON>, Dispatcher, types
from aiogram.filters import Command
import asyncio

from app.core.config import settings

logger = structlog.get_logger()


class TelegramService:
    """Сервис для работы с Telegram"""
    
    def __init__(self):
        self.bot: Optional[Bot] = None
        self.dp: Optional[Dispatcher] = None
        
        if settings.TELEGRAM_BOT_TOKEN:
            self.bot = Bot(token=settings.TELEGRAM_BOT_TOKEN)
            self.dp = Dispatcher()
            self._setup_handlers()
    
    def _setup_handlers(self):
        """Настройка обработчиков команд"""
        if not self.dp:
            return
        
        @self.dp.message(Command("start"))
        async def start_command(message: types.Message):
            await message.answer(
                "🤖 Добро пожаловать в Grid Trading Bot!\n\n"
                "Этот бот будет отправлять вам уведомления о торговле:\n"
                "• Запуск/остановка стратегий\n"
                "• Исполнение ордеров\n"
                "• Срабатывание Take Profit/Stop Loss\n\n"
                "Для управления стратегиями используйте веб-интерфейс:\n"
                "http://localhost:3000"
            )
        
        @self.dp.message(Command("status"))
        async def status_command(message: types.Message):
            # Здесь будет логика получения статуса стратегий
            await message.answer(
                "📊 Статус торговых стратегий:\n\n"
                "Активных стратегий: 0\n"
                "Общая прибыль: 0.00 USDT\n"
                "Всего сделок: 0\n\n"
                "Для подробной информации используйте веб-интерфейс."
            )
        
        @self.dp.message(Command("help"))
        async def help_command(message: types.Message):
            await message.answer(
                "🆘 Доступные команды:\n\n"
                "/start - Приветствие\n"
                "/status - Статус стратегий\n"
                "/help - Эта справка\n\n"
                "Управление стратегиями доступно только через веб-интерфейс."
            )
    
    async def send_notification(self, user_id: int, message: str):
        """Отправить уведомление пользователю"""
        if not self.bot:
            logger.warning("Telegram бот не настроен")
            return False
        
        try:
            await self.bot.send_message(chat_id=user_id, text=message)
            logger.info(f"Уведомление отправлено пользователю {user_id}")
            return True
        except Exception as e:
            logger.error(f"Ошибка отправки уведомления: {e}")
            return False
    
    async def send_strategy_started(self, user_id: int, strategy_name: str):
        """Уведомление о запуске стратегии"""
        message = f"🚀 Стратегия '{strategy_name}' запущена"
        await self.send_notification(user_id, message)
    
    async def send_strategy_stopped(self, user_id: int, strategy_name: str):
        """Уведомление об остановке стратегии"""
        message = f"🛑 Стратегия '{strategy_name}' остановлена"
        await self.send_notification(user_id, message)
    
    async def send_order_filled(self, user_id: int, order_data: dict):
        """Уведомление об исполнении ордера"""
        message = (
            f"✅ Ордер исполнен:\n"
            f"• {order_data['side'].upper()} {order_data['amount']} {order_data['symbol']}\n"
            f"• Цена: {order_data['price']} USDT\n"
            f"• Стоимость: {order_data.get('cost', 0):.2f} USDT"
        )
        await self.send_notification(user_id, message)
    
    async def send_take_profit(self, user_id: int, strategy_name: str, profit: float):
        """Уведомление о срабатывании Take Profit"""
        message = (
            f"🎯 Take Profit сработал!\n"
            f"• Стратегия: {strategy_name}\n"
            f"• Прибыль: {profit:.2f} USDT"
        )
        await self.send_notification(user_id, message)
    
    async def send_stop_loss(self, user_id: int, strategy_name: str, loss: float):
        """Уведомление о срабатывании Stop Loss"""
        message = (
            f"🛡️ Stop Loss сработал!\n"
            f"• Стратегия: {strategy_name}\n"
            f"• Убыток: {abs(loss):.2f} USDT"
        )
        await self.send_notification(user_id, message)
    
    async def send_error_notification(self, user_id: int, strategy_name: str, error: str):
        """Уведомление об ошибке"""
        message = (
            f"❌ Ошибка в стратегии '{strategy_name}':\n"
            f"{error}\n\n"
            f"Проверьте настройки в веб-интерфейсе."
        )
        await self.send_notification(user_id, message)
    
    async def start_polling(self):
        """Запустить polling для получения сообщений"""
        if not self.dp or not self.bot:
            logger.warning("Telegram бот не настроен")
            return
        
        try:
            logger.info("Запуск Telegram бота...")
            await self.dp.start_polling(self.bot)
        except Exception as e:
            logger.error(f"Ошибка запуска Telegram бота: {e}")
    
    async def stop(self):
        """Остановить бота"""
        if self.bot:
            await self.bot.session.close()


# Глобальный экземпляр сервиса
telegram_service = TelegramService()
