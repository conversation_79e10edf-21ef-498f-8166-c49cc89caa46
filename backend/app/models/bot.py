"""
Модель сеточного бота.
"""

from sqlalchemy import Column, Integer, String, Boolean, Numeric, DateTime, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.database import Base


class GridBot(Base):
    """
    Модель сеточного торгового бота.
    """
    __tablename__ = "grid_bots"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Торговые параметры
    symbol = Column(String(20), nullable=False, index=True)  # Торговая пара (например, BTC/USDT)
    exchange_key_id = Column(Integer, ForeignKey("exchange_keys.id"), nullable=True)  # ID ключа биржи
    lower_price = Column(Numeric(20, 8), nullable=False)     # Нижняя граница сетки
    upper_price = Column(Numeric(20, 8), nullable=False)     # Верхняя граница сетки
    grid_count = Column(Integer, nullable=False)             # Количество уровней сетки
    order_amount = Column(Numeric(20, 8), nullable=False)    # Размер ордера
    profit_percentage = Column(Numeric(5, 2), nullable=False, default=1.0)  # Процент прибыли
    
    # Управление
    is_active = Column(Boolean, default=False, nullable=False)
    
    # Статистика
    total_profit = Column(Numeric(20, 8), default=0, nullable=False)
    total_trades = Column(Integer, default=0, nullable=False)
    
    # Временные метки
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    started_at = Column(DateTime(timezone=True), nullable=True)
    stopped_at = Column(DateTime(timezone=True), nullable=True)
    
    # Связи
    orders = relationship("Order", back_populates="bot", cascade="all, delete-orphan")
    exchange_key = relationship("ExchangeKey", back_populates="bots")
    
    def __repr__(self):
        return f"<GridBot(id={self.id}, name='{self.name}', symbol='{self.symbol}', active={self.is_active})>"
    
    @property
    def price_range(self):
        """Возвращает диапазон цен сетки."""
        return self.upper_price - self.lower_price
    
    @property
    def grid_step(self):
        """Возвращает размер шага сетки."""
        if self.grid_count <= 1:
            return 0
        return self.price_range / (self.grid_count - 1)
    
    def get_grid_levels(self):
        """Возвращает список уровней сетки."""
        if self.grid_count <= 1:
            return [self.lower_price]
        
        levels = []
        step = self.grid_step
        for i in range(self.grid_count):
            level = self.lower_price + (step * i)
            levels.append(level)
        
        return levels
