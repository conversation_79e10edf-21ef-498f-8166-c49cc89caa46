"""
Модель для хранения API ключей бирж.
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum as SQLEnum, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum
import json
from cryptography.fernet import Fernet
import os

from app.database import Base


class ExchangeType(str, Enum):
    """Типы бирж"""
    BINANCE = "binance"
    BYBIT = "bybit"
    OKX = "okx"
    KUCOIN = "kucoin"
    GATE = "gate"
    HUOBI = "huobi"
    BITGET = "bitget"


class NetworkType(str, Enum):
    """Типы сетей"""
    LIVE = "live"
    TESTNET = "testnet"


class ExchangeKey(Base):
    """Модель API ключей биржи"""

    __tablename__ = "exchange_keys"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, comment="Название ключа")

    # Владелец ключа
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)

    exchange = Column(SQLEnum(ExchangeType), nullable=False, comment="Тип биржи")
    network = Column(SQLEnum(NetworkType), nullable=False, comment="Тип сети")
    
    # Зашифрованные данные
    api_key_encrypted = Column(Text, nullable=False, comment="Зашифрованный API ключ")
    secret_encrypted = Column(Text, nullable=False, comment="Зашифрованный секретный ключ")
    passphrase_encrypted = Column(Text, nullable=True, comment="Зашифрованная фраза (для некоторых бирж)")
    
    # Статус и валидация
    is_active = Column(Boolean, default=True, comment="Активен ли ключ")
    is_validated = Column(Boolean, default=False, comment="Прошел ли валидацию")
    last_validation = Column(DateTime, nullable=True, comment="Последняя проверка")
    validation_error = Column(Text, nullable=True, comment="Ошибка валидации")
    
    # Информация о балансе (кэш)
    balance_cache = Column(Text, nullable=True, comment="Кэш баланса в JSON")
    balance_updated = Column(DateTime, nullable=True, comment="Время обновления баланса")
    
    # Метаданные
    created_at = Column(DateTime, default=func.now(), comment="Время создания")
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment="Время обновления")
    
    # Связи
    user = relationship("User", back_populates="exchange_keys")
    bots = relationship("GridBot", back_populates="exchange_key")
    
    @property
    def encryption_key(self) -> bytes:
        """Получить ключ шифрования"""
        key = os.getenv("ENCRYPTION_KEY")
        if not key:
            # В продакшене должен быть установлен через переменную окружения
            key = "your-secret-encryption-key-32-bytes"
        
        # Убеждаемся, что ключ имеет правильную длину
        if len(key) < 32:
            key = key.ljust(32, '0')
        elif len(key) > 32:
            key = key[:32]
            
        return key.encode()
    
    def encrypt_data(self, data: str) -> str:
        """Зашифровать данные"""
        if not data:
            return ""
        
        fernet = Fernet(Fernet.generate_key())
        # Для простоты используем базовое шифрование
        # В продакшене нужно использовать более безопасный подход
        return data  # Временно без шифрования для разработки
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """Расшифровать данные"""
        if not encrypted_data:
            return ""
        
        # Временно без расшифровки для разработки
        return encrypted_data
    
    @property
    def api_key(self) -> str:
        """Получить расшифрованный API ключ"""
        return self.decrypt_data(self.api_key_encrypted)
    
    @api_key.setter
    def api_key(self, value: str):
        """Установить API ключ с шифрованием"""
        self.api_key_encrypted = self.encrypt_data(value)
    
    @property
    def secret(self) -> str:
        """Получить расшифрованный секретный ключ"""
        return self.decrypt_data(self.secret_encrypted)
    
    @secret.setter
    def secret(self, value: str):
        """Установить секретный ключ с шифрованием"""
        self.secret_encrypted = self.encrypt_data(value)
    
    @property
    def passphrase(self) -> str:
        """Получить расшифрованную фразу"""
        if not self.passphrase_encrypted:
            return ""
        return self.decrypt_data(self.passphrase_encrypted)
    
    @passphrase.setter
    def passphrase(self, value: str):
        """Установить фразу с шифрованием"""
        if value:
            self.passphrase_encrypted = self.encrypt_data(value)
        else:
            self.passphrase_encrypted = None
    
    @property
    def balance(self) -> dict:
        """Получить кэшированный баланс"""
        if not self.balance_cache:
            return {}
        try:
            return json.loads(self.balance_cache)
        except json.JSONDecodeError:
            return {}
    
    @balance.setter
    def balance(self, value: dict):
        """Установить баланс в кэш"""
        self.balance_cache = json.dumps(value) if value else None
        self.balance_updated = func.now()
    
    def get_ccxt_config(self) -> dict:
        """Получить конфигурацию для CCXT"""
        config = {
            'apiKey': self.api_key,
            'secret': self.secret,
            'sandbox': self.network == NetworkType.TESTNET,
            'enableRateLimit': True,
        }
        
        # Добавляем passphrase для бирж, которые его требуют
        if self.passphrase and self.exchange in [ExchangeType.OKX, ExchangeType.KUCOIN]:
            config['password'] = self.passphrase
        
        return config
    
    def __repr__(self):
        return f"<ExchangeKey(id={self.id}, name='{self.name}', exchange='{self.exchange}', network='{self.network}')>"
