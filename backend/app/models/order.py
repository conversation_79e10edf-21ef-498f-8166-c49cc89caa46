"""
Модель ордера.
"""

import enum
from sqlalchemy import Column, Integer, String, Numeric, DateTime, ForeignKey, Enum, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.database import Base


class OrderType(enum.Enum):
    """Тип ордера."""
    BUY = "buy"
    SELL = "sell"


class OrderStatus(enum.Enum):
    """Статус ордера."""
    PENDING = "pending"      # Ожидает исполнения
    FILLED = "filled"        # Исполнен
    CANCELLED = "cancelled"  # Отменен
    FAILED = "failed"        # Ошибка
    COMPLETED = "completed"  # Обработан (создан встречный ордер)


class Order(Base):
    """
    Модель торгового ордера.
    """
    __tablename__ = "orders"

    id = Column(Integer, primary_key=True, index=True)

    # Связь с ботом
    bot_id = Column(Integer, ForeignKey("grid_bots.id"), nullable=False, index=True)

    # Параметры ордера
    symbol = Column(String(20), nullable=False, index=True)
    order_type = Column(Enum(OrderType), nullable=False)
    amount = Column(Numeric(20, 8), nullable=False)
    price = Column(Numeric(20, 8), nullable=False)

    # Статус и результат
    status = Column(Enum(OrderStatus), default=OrderStatus.PENDING, nullable=False, index=True)
    filled_amount = Column(Numeric(20, 8), default=0, nullable=False)
    filled_price = Column(Numeric(20, 8), nullable=True)

    # Внешние идентификаторы
    exchange_order_id = Column(String(100), nullable=True, index=True)  # ID ордера на бирже

    # Связи
    parent_order_id = Column(Integer, ForeignKey("orders.id"), nullable=True)  # Родительский ордер
    parent_order = relationship("Order", remote_side=[id], backref="child_orders")

    # Дополнительная информация
    error_message = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)

    # Временные метки
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    filled_at = Column(DateTime(timezone=True), nullable=True)

    # Связи
    bot = relationship("GridBot", back_populates="orders")

    def __repr__(self):
        return f"<Order(id={self.id}, bot_id={self.bot_id}, type={self.order_type.value}, " \
               f"amount={self.amount}, price={self.price}, status={self.status.value})>"

    @property
    def is_buy(self):
        """Проверяет, является ли ордер покупкой."""
        return self.order_type == OrderType.BUY

    @property
    def is_sell(self):
        """Проверяет, является ли ордер продажей."""
        return self.order_type == OrderType.SELL

    @property
    def is_filled(self):
        """Проверяет, исполнен ли ордер."""
        return self.status == OrderStatus.FILLED

    @property
    def is_pending(self):
        """Проверяет, ожидает ли ордер исполнения."""
        return self.status == OrderStatus.PENDING

    @property
    def total_value(self):
        """Возвращает общую стоимость ордера."""
        return self.amount * self.price

    @property
    def filled_value(self):
        """Возвращает стоимость исполненной части ордера."""
        if self.filled_price:
            return self.filled_amount * self.filled_price
        return self.filled_amount * self.price
