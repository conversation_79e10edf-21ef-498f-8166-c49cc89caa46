"""
Модель пользователя с полной системой аутентификации.
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, Enum as SQLEnum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum
from passlib.context import CryptContext
import secrets
import pyotp
import qrcode
from io import BytesIO
import base64

from app.database import Base

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class UserRole(str, Enum):
    """Роли пользователей"""
    USER = "user"
    ADMIN = "admin"
    MODERATOR = "moderator"


class UserStatus(str, Enum):
    """Статусы пользователей"""
    PENDING = "pending"          # Ожидает подтверждения email
    ACTIVE = "active"            # Активный пользователь
    SUSPENDED = "suspended"      # Заблокирован
    DELETED = "deleted"          # Удален


class User(Base):
    """Модель пользователя"""
    
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Основная информация
    username = Column(String(50), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False, index=True)
    full_name = Column(String(100), nullable=True)
    
    # Аутентификация
    hashed_password = Column(String(255), nullable=False)
    is_email_verified = Column(Boolean, default=False, nullable=False)
    
    # Роли и статус
    role = Column(SQLEnum(UserRole), default=UserRole.USER, nullable=False)
    status = Column(SQLEnum(UserStatus), default=UserStatus.PENDING, nullable=False)
    
    # OTP (двухфакторная аутентификация)
    otp_secret = Column(String(32), nullable=True)
    is_otp_enabled = Column(Boolean, default=False, nullable=False)
    backup_codes = Column(Text, nullable=True)  # JSON массив резервных кодов
    
    # Токены
    email_verification_token = Column(String(255), nullable=True)
    password_reset_token = Column(String(255), nullable=True)
    password_reset_expires = Column(DateTime(timezone=True), nullable=True)
    
    # Временные метки
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)
    email_verified_at = Column(DateTime(timezone=True), nullable=True)
    
    # Связи
    exchange_keys = relationship("ExchangeKey", back_populates="user", cascade="all, delete-orphan")
    bots = relationship("GridBot", back_populates="user", cascade="all, delete-orphan")
    
    def set_password(self, password: str) -> None:
        """Установить пароль пользователя"""
        self.hashed_password = pwd_context.hash(password)
    
    def verify_password(self, password: str) -> bool:
        """Проверить пароль пользователя"""
        return pwd_context.verify(password, self.hashed_password)
    
    def generate_email_verification_token(self) -> str:
        """Сгенерировать токен для подтверждения email"""
        token = secrets.token_urlsafe(32)
        self.email_verification_token = token
        return token
    
    def generate_password_reset_token(self) -> str:
        """Сгенерировать токен для сброса пароля"""
        token = secrets.token_urlsafe(32)
        self.password_reset_token = token
        return token
    
    def setup_otp(self) -> tuple[str, str]:
        """Настроить OTP и вернуть секрет и QR код"""
        if not self.otp_secret:
            self.otp_secret = pyotp.random_base32()
        
        # Создаем TOTP объект
        totp = pyotp.TOTP(self.otp_secret)
        
        # Генерируем URI для QR кода
        provisioning_uri = totp.provisioning_uri(
            name=self.email,
            issuer_name="Earnlyze"
        )
        
        # Создаем QR код
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(provisioning_uri)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Конвертируем в base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        return self.otp_secret, qr_code_base64
    
    def verify_otp(self, token: str) -> bool:
        """Проверить OTP токен"""
        if not self.otp_secret:
            return False
        
        totp = pyotp.TOTP(self.otp_secret)
        return totp.verify(token)
    
    def generate_backup_codes(self) -> list[str]:
        """Сгенерировать резервные коды"""
        import json
        
        codes = [secrets.token_hex(4).upper() for _ in range(10)]
        self.backup_codes = json.dumps(codes)
        return codes
    
    def verify_backup_code(self, code: str) -> bool:
        """Проверить и использовать резервный код"""
        if not self.backup_codes:
            return False
        
        import json
        codes = json.loads(self.backup_codes)
        
        if code.upper() in codes:
            codes.remove(code.upper())
            self.backup_codes = json.dumps(codes)
            return True
        
        return False
    
    def get_backup_codes(self) -> list[str]:
        """Получить список резервных кодов"""
        if not self.backup_codes:
            return []
        
        import json
        return json.loads(self.backup_codes)
    
    @property
    def is_active(self) -> bool:
        """Проверить, активен ли пользователь"""
        return self.status == UserStatus.ACTIVE
    
    @property
    def is_admin(self) -> bool:
        """Проверить, является ли пользователь администратором"""
        return self.role == UserRole.ADMIN
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}', role='{self.role}')>"
