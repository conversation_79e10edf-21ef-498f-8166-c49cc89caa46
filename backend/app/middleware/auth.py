"""
Middleware для аутентификации
"""

import jwt
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.config import settings
from app.database import AsyncSessionLocal
from app.models.user import User, UserStatus


class AuthMiddleware(BaseHTTPMiddleware):
    """Middleware для автоматической проверки JWT токенов"""
    
    # Пути, которые не требуют аутентификации
    EXCLUDED_PATHS = {
        "/",
        "/docs",
        "/openapi.json",
        "/redoc",
        "/api/v1/auth/register",
        "/api/v1/auth/login",
        "/api/v1/auth/verify-email",
        "/api/v1/auth/request-password-reset",
        "/api/v1/auth/reset-password",
        "/health",
        "/metrics"
    }
    
    async def dispatch(self, request: Request, call_next):
        """Обработка запроса"""
        
        # Проверяем, нужна ли аутентификация для этого пути
        if self._is_excluded_path(request.url.path):
            return await call_next(request)
        
        # Получаем токен из заголовка
        authorization = request.headers.get("Authorization")
        if not authorization:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Токен аутентификации не предоставлен"}
            )
        
        try:
            # Извлекаем токен
            scheme, token = authorization.split()
            if scheme.lower() != "bearer":
                raise ValueError("Неверная схема аутентификации")
            
            # Проверяем токен
            user = await self._verify_token(token)
            if not user:
                raise ValueError("Неверный токен")
            
            # Добавляем пользователя в request state
            request.state.user = user
            
        except Exception as e:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Неверный токен аутентификации"}
            )
        
        return await call_next(request)
    
    def _is_excluded_path(self, path: str) -> bool:
        """Проверяет, исключен ли путь из аутентификации"""
        # Точное совпадение
        if path in self.EXCLUDED_PATHS:
            return True
        
        # Проверяем префиксы для статических файлов
        excluded_prefixes = ["/static/", "/favicon.ico"]
        for prefix in excluded_prefixes:
            if path.startswith(prefix):
                return True
        
        return False
    
    async def _verify_token(self, token: str) -> User:
        """Проверяет JWT токен и возвращает пользователя"""
        try:
            # Декодируем токен
            payload = jwt.decode(
                token, 
                settings.SECRET_KEY, 
                algorithms=[settings.ALGORITHM]
            )
            
            user_id = payload.get("sub")
            if not user_id:
                return None
            
            # Получаем пользователя из базы данных
            async with AsyncSessionLocal() as db:
                result = await db.execute(
                    select(User).where(User.id == int(user_id))
                )
                user = result.scalars().first()
                
                if not user:
                    return None
                
                # Проверяем статус пользователя
                if user.status != UserStatus.ACTIVE:
                    return None
                
                return user
                
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
        except Exception:
            return None


class OptionalAuthMiddleware(BaseHTTPMiddleware):
    """Middleware для опциональной аутентификации (не блокирует запросы без токена)"""
    
    async def dispatch(self, request: Request, call_next):
        """Обработка запроса"""
        
        # Получаем токен из заголовка
        authorization = request.headers.get("Authorization")
        if authorization:
            try:
                # Извлекаем токен
                scheme, token = authorization.split()
                if scheme.lower() == "bearer":
                    # Проверяем токен
                    user = await self._verify_token(token)
                    if user:
                        request.state.user = user
            except Exception:
                # Игнорируем ошибки токена для опциональной аутентификации
                pass
        
        return await call_next(request)
    
    async def _verify_token(self, token: str) -> User:
        """Проверяет JWT токен и возвращает пользователя"""
        try:
            # Декодируем токен
            payload = jwt.decode(
                token, 
                settings.SECRET_KEY, 
                algorithms=[settings.ALGORITHM]
            )
            
            user_id = payload.get("sub")
            if not user_id:
                return None
            
            # Получаем пользователя из базы данных
            async with AsyncSessionLocal() as db:
                result = await db.execute(
                    select(User).where(User.id == int(user_id))
                )
                user = result.scalars().first()
                
                if not user:
                    return None
                
                # Проверяем статус пользователя
                if user.status != UserStatus.ACTIVE:
                    return None
                
                return user
                
        except Exception:
            return None


def get_current_user_from_request(request: Request) -> User:
    """Получить текущего пользователя из request state"""
    user = getattr(request.state, 'user', None)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Пользователь не аутентифицирован"
        )
    return user


def get_optional_user_from_request(request: Request) -> User:
    """Получить текущего пользователя из request state (может быть None)"""
    return getattr(request.state, 'user', None)
