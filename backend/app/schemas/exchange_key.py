"""
Схемы Pydantic для API ключей бирж.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, validator

from app.models.exchange_key import ExchangeType, NetworkType


class ExchangeKeyBase(BaseModel):
    """Базовая схема ключа биржи."""
    name: str = Field(..., min_length=1, max_length=100, description="Название ключа")
    exchange: ExchangeType = Field(..., description="Тип биржи")
    network: NetworkType = Field(..., description="Тип сети (live/testnet)")


class ExchangeKeyCreate(ExchangeKeyBase):
    """Схема для создания ключа биржи."""
    api_key: str = Field(..., min_length=1, description="API ключ")
    secret: str = Field(..., min_length=1, description="Секретный ключ")
    passphrase: Optional[str] = Field(None, description="Фраза (для OKX, KuCoin)")

    @validator('passphrase')
    def validate_passphrase(cls, v, values):
        """Проверяем обязательность passphrase для некоторых бирж."""
        if 'exchange' in values:
            exchange = values['exchange']
            if exchange in [ExchangeType.OKX, ExchangeType.KUCOIN] and not v:
                raise ValueError(f'Для биржи {exchange} требуется passphrase')
        return v


class ExchangeKeyUpdate(BaseModel):
    """Схема для обновления ключа биржи."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    api_key: Optional[str] = Field(None, min_length=1)
    secret: Optional[str] = Field(None, min_length=1)
    passphrase: Optional[str] = None
    is_active: Optional[bool] = None


class ExchangeKeyResponse(ExchangeKeyBase):
    """Схема ответа с информацией о ключе."""
    id: int
    is_active: bool
    is_validated: bool
    last_validation: Optional[datetime]
    validation_error: Optional[str]
    balance_updated: Optional[datetime]
    created_at: datetime
    updated_at: Optional[datetime]

    class Config:
        from_attributes = True


class ExchangeKeyValidation(BaseModel):
    """Результат валидации ключа."""
    key_id: int
    valid: bool
    error: Optional[str]
    balance: Dict[str, Any]
    permissions: Dict[str, bool]
    exchange_info: Dict[str, Any]


class BalanceInfo(BaseModel):
    """Информация о балансе."""
    free: float = Field(0.0, description="Доступно для торговли")
    used: float = Field(0.0, description="Заблокировано в ордерах")
    total: float = Field(0.0, description="Общий баланс")


class BalanceResponse(BaseModel):
    """Ответ с балансом."""
    balance: Dict[str, BalanceInfo]
    cached: bool
    updated_at: datetime
    timestamp: Optional[int]
    datetime: Optional[str]


class ExchangeInfo(BaseModel):
    """Информация о поддерживаемой бирже."""
    id: str
    name: str
    countries: List[str]
    urls: Dict[str, Any]
    has: Dict[str, bool]
    requires_passphrase: bool


class ExchangeCapabilities(BaseModel):
    """Возможности биржи."""
    spot: bool = Field(False, description="Спотовая торговля")
    future: bool = Field(False, description="Фьючерсная торговля")
    fetchBalance: bool = Field(False, description="Получение баланса")
    createOrder: bool = Field(False, description="Создание ордеров")
    cancelOrder: bool = Field(False, description="Отмена ордеров")
    fetchOrders: bool = Field(False, description="Получение ордеров")
    fetchMyTrades: bool = Field(False, description="Получение сделок")


class ExchangeKeyStats(BaseModel):
    """Статистика использования ключа."""
    key_id: int
    name: str
    exchange: str
    network: str
    is_active: bool
    is_validated: bool
    last_validation: Optional[datetime]
    
    # Статистика ботов
    total_bots: int = 0
    active_bots: int = 0
    
    # Баланс
    total_balance_usd: float = 0.0
    main_currencies: Dict[str, float] = {}
    
    # Последняя активность
    last_used: Optional[datetime] = None


class ExchangeKeyListResponse(BaseModel):
    """Ответ со списком ключей."""
    keys: List[ExchangeKeyResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool


class ExchangeTestConnection(BaseModel):
    """Тест подключения к бирже."""
    exchange: ExchangeType
    network: NetworkType
    api_key: str
    secret: str
    passphrase: Optional[str] = None


class ExchangeTestResult(BaseModel):
    """Результат теста подключения."""
    success: bool
    exchange_name: str
    network: str
    balance: Optional[Dict[str, Any]] = None
    permissions: Optional[Dict[str, bool]] = None
    error: Optional[str] = None
    response_time_ms: Optional[int] = None


class ExchangeKeySecure(BaseModel):
    """Безопасная схема ключа (без секретных данных)."""
    id: int
    name: str
    exchange: ExchangeType
    network: NetworkType
    is_active: bool
    is_validated: bool
    last_validation: Optional[datetime]
    validation_error: Optional[str]
    balance_updated: Optional[datetime]
    created_at: datetime
    
    # Маскированные данные
    api_key_masked: str = Field(..., description="Замаскированный API ключ")
    has_passphrase: bool = Field(False, description="Есть ли passphrase")

    class Config:
        from_attributes = True


class ExchangeKeyImport(BaseModel):
    """Импорт ключей из файла."""
    keys: List[ExchangeKeyCreate]
    overwrite_existing: bool = Field(False, description="Перезаписать существующие")


class ExchangeKeyExport(BaseModel):
    """Экспорт ключей."""
    include_secrets: bool = Field(False, description="Включить секретные данные")
    exchange_filter: Optional[ExchangeType] = None
    network_filter: Optional[NetworkType] = None
    active_only: bool = Field(True, description="Только активные ключи")


class ExchangeKeyBackup(BaseModel):
    """Резервная копия ключа."""
    name: str
    exchange: ExchangeType
    network: NetworkType
    api_key_encrypted: str
    secret_encrypted: str
    passphrase_encrypted: Optional[str]
    created_at: datetime
    metadata: Dict[str, Any] = {}


class ExchangeRateLimit(BaseModel):
    """Информация о лимитах биржи."""
    requests_per_second: int
    requests_per_minute: int
    requests_per_hour: int
    weight_limit: Optional[int] = None
    current_weight: Optional[int] = None


class ExchangeStatus(BaseModel):
    """Статус биржи."""
    exchange: str
    status: str  # 'online', 'maintenance', 'offline'
    message: Optional[str] = None
    estimated_recovery: Optional[datetime] = None
    affected_endpoints: List[str] = []
