"""
Схемы для работы с пользователями.
"""

from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field, validator
from app.models.user import UserRole, UserStatus


class UserBase(BaseModel):
    """Базовая схема пользователя"""
    username: str = Field(..., min_length=3, max_length=50, description="Имя пользователя")
    email: EmailStr = Field(..., description="Email адрес")
    full_name: Optional[str] = Field(None, max_length=100, description="Полное имя")


class UserCreate(UserBase):
    """Схема для создания пользователя"""
    password: str = Field(..., min_length=8, max_length=100, description="Пароль")
    
    @validator('password')
    def validate_password(cls, v):
        """Валидация пароля"""
        if len(v) < 8:
            raise ValueError('Пароль должен содержать минимум 8 символов')
        if not any(c.isupper() for c in v):
            raise ValueError('Пароль должен содержать хотя бы одну заглавную букву')
        if not any(c.islower() for c in v):
            raise ValueError('Пароль должен содержать хотя бы одну строчную букву')
        if not any(c.isdigit() for c in v):
            raise ValueError('Пароль должен содержать хотя бы одну цифру')
        return v


class UserUpdate(BaseModel):
    """Схема для обновления пользователя"""
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, max_length=100)


class UserPasswordUpdate(BaseModel):
    """Схема для смены пароля"""
    current_password: str = Field(..., description="Текущий пароль")
    new_password: str = Field(..., min_length=8, max_length=100, description="Новый пароль")
    
    @validator('new_password')
    def validate_password(cls, v):
        """Валидация пароля"""
        if len(v) < 8:
            raise ValueError('Пароль должен содержать минимум 8 символов')
        if not any(c.isupper() for c in v):
            raise ValueError('Пароль должен содержать хотя бы одну заглавную букву')
        if not any(c.islower() for c in v):
            raise ValueError('Пароль должен содержать хотя бы одну строчную букву')
        if not any(c.isdigit() for c in v):
            raise ValueError('Пароль должен содержать хотя бы одну цифру')
        return v


class UserResponse(UserBase):
    """Схема ответа с данными пользователя"""
    id: int
    role: UserRole
    status: UserStatus
    is_email_verified: bool
    is_otp_enabled: bool
    created_at: datetime
    updated_at: Optional[datetime]
    last_login: Optional[datetime]
    email_verified_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """Схема для входа"""
    username: str = Field(..., description="Имя пользователя или email")
    password: str = Field(..., description="Пароль")
    otp_token: Optional[str] = Field(None, description="OTP токен (если включен)")


class UserRegister(UserCreate):
    """Схема для регистрации"""
    pass


class EmailVerificationRequest(BaseModel):
    """Запрос на подтверждение email"""
    token: str = Field(..., description="Токен подтверждения")


class PasswordResetRequest(BaseModel):
    """Запрос на сброс пароля"""
    email: EmailStr = Field(..., description="Email для сброса пароля")


class PasswordResetConfirm(BaseModel):
    """Подтверждение сброса пароля"""
    token: str = Field(..., description="Токен сброса пароля")
    new_password: str = Field(..., min_length=8, max_length=100, description="Новый пароль")
    
    @validator('new_password')
    def validate_password(cls, v):
        """Валидация пароля"""
        if len(v) < 8:
            raise ValueError('Пароль должен содержать минимум 8 символов')
        if not any(c.isupper() for c in v):
            raise ValueError('Пароль должен содержать хотя бы одну заглавную букву')
        if not any(c.islower() for c in v):
            raise ValueError('Пароль должен содержать хотя бы одну строчную букву')
        if not any(c.isdigit() for c in v):
            raise ValueError('Пароль должен содержать хотя бы одну цифру')
        return v


class OTPSetupResponse(BaseModel):
    """Ответ настройки OTP"""
    secret: str = Field(..., description="Секретный ключ OTP")
    qr_code: str = Field(..., description="QR код в base64")
    backup_codes: List[str] = Field(..., description="Резервные коды")


class OTPVerifyRequest(BaseModel):
    """Запрос на проверку OTP"""
    token: str = Field(..., description="OTP токен")


class OTPDisableRequest(BaseModel):
    """Запрос на отключение OTP"""
    password: str = Field(..., description="Пароль пользователя")
    token: Optional[str] = Field(None, description="OTP токен или резервный код")


class BackupCodesResponse(BaseModel):
    """Ответ с резервными кодами"""
    codes: List[str] = Field(..., description="Резервные коды")


class TokenResponse(BaseModel):
    """Ответ с токеном"""
    access_token: str = Field(..., description="JWT токен доступа")
    token_type: str = Field("bearer", description="Тип токена")
    expires_in: int = Field(..., description="Время жизни токена в секундах")
    user: UserResponse = Field(..., description="Данные пользователя")


class AdminUserUpdate(BaseModel):
    """Схема для обновления пользователя администратором"""
    username: Optional[str] = Field(None, min_length=3, max_length=50)
    email: Optional[EmailStr] = None
    full_name: Optional[str] = Field(None, max_length=100)
    role: Optional[UserRole] = None
    status: Optional[UserStatus] = None
    is_email_verified: Optional[bool] = None
    is_otp_enabled: Optional[bool] = None


class UserListResponse(BaseModel):
    """Ответ со списком пользователей"""
    users: List[UserResponse]
    total: int
    page: int
    per_page: int
    pages: int
