"""
Схемы для стратегий
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field, validator
from uuid import UUID

from app.models.enums import StrategyStatus, Direction, MarginMode


class StrategyBase(BaseModel):
    """Базовая схема стратегии"""
    name: str = Field(..., min_length=1, max_length=100, description="Название стратегии")
    description: Optional[str] = Field(None, max_length=500, description="Описание стратегии")
    
    # Настройки биржи
    exchange: str = Field(..., description="Биржа (binance, bybit)")
    symbol: str = Field(..., description="Торговая пара (BTC/USDT:USDT)")
    direction: Direction = Field(..., description="Направление торговли")
    margin_mode: MarginMode = Field(MarginMode.ISOLATED, description="Режим маржи")
    
    # Параметры сетки
    grid_count: int = Field(..., ge=2, le=20, description="Количество уровней сетки")
    grid_step_percent: float = Field(..., ge=0.1, le=10.0, description="Шаг сетки в %")
    volume_multiplier: float = Field(1.75, ge=1.0, le=5.0, description="Мультипликатор объема")
    
    # Управление капиталом
    deposit_amount: float = Field(..., ge=10.0, le=50000.0, description="Размер депозита")
    leverage: float = Field(1.0, ge=1.0, le=20.0, description="Плечо")
    
    # Риск-менеджмент
    tp_percent: float = Field(..., ge=0.1, le=100.0, description="Take Profit %")
    sl_percent: Optional[float] = Field(None, ge=0.1, le=50.0, description="Stop Loss %")
    max_drawdown_percent: float = Field(10.0, ge=1.0, le=20.0, description="Максимальная просадка %")
    
    @validator('exchange')
    def validate_exchange(cls, v):
        allowed_exchanges = ['binance', 'bybit']
        if v not in allowed_exchanges:
            raise ValueError(f'Биржа должна быть одной из: {allowed_exchanges}')
        return v
    
    @validator('symbol')
    def validate_symbol(cls, v):
        # Проверка формата символа для фьючерсов
        if ':' not in v:
            raise ValueError('Символ должен быть в формате BASE/QUOTE:SETTLE для фьючерсов')
        return v


class StrategyCreate(StrategyBase):
    """Схема для создания стратегии"""
    pass


class StrategyUpdate(BaseModel):
    """Схема для обновления стратегии"""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    
    # Параметры сетки (можно изменять только у неактивных стратегий)
    grid_count: Optional[int] = Field(None, ge=2, le=20)
    grid_step_percent: Optional[float] = Field(None, ge=0.1, le=10.0)
    volume_multiplier: Optional[float] = Field(None, ge=1.0, le=5.0)
    
    # Управление капиталом
    deposit_amount: Optional[float] = Field(None, ge=10.0, le=50000.0)
    leverage: Optional[float] = Field(None, ge=1.0, le=20.0)
    
    # Риск-менеджмент
    tp_percent: Optional[float] = Field(None, ge=0.1, le=100.0)
    sl_percent: Optional[float] = Field(None, ge=0.1, le=50.0)
    max_drawdown_percent: Optional[float] = Field(None, ge=1.0, le=20.0)


class StrategyResponse(StrategyBase):
    """Схема ответа стратегии"""
    id: UUID
    user_id: UUID
    
    # Статус и состояние
    status: StrategyStatus
    is_active: bool
    
    # Статистика
    total_profit: float
    total_trades: int
    win_rate: float
    current_drawdown: float
    
    # Текущие позиции
    current_position_size: float
    current_position_value: float
    average_entry_price: Optional[float]
    
    # Временные метки
    created_at: datetime
    updated_at: datetime
    started_at: Optional[datetime]
    stopped_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class StrategyStats(BaseModel):
    """Статистика стратегии"""
    total_profit: float
    total_trades: int
    win_rate: float
    current_drawdown: float
    max_drawdown: float
    sharpe_ratio: Optional[float]
    profit_factor: Optional[float]
    
    # Статистика по периодам
    daily_profit: float
    weekly_profit: float
    monthly_profit: float
    
    # Статистика по сделкам
    avg_profit_per_trade: float
    avg_loss_per_trade: float
    largest_win: float
    largest_loss: float
    
    # Статистика по времени
    avg_trade_duration: Optional[float]  # в минутах
    total_trading_time: Optional[float]  # в часах


class StrategyRiskAssessment(BaseModel):
    """Оценка рисков стратегии"""
    risk_level: str  # low, medium, high
    max_loss: float
    risk_reward_ratio: float
    leverage_risk: str
    grid_risk: str
    symbol_volatility: str
    
    # Рекомендации
    recommendations: list[str]
    warnings: list[str]
