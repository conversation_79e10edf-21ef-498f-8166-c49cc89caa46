"""
Схемы Pydantic для торговых ботов.
"""

from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field, validator


class BotBase(BaseModel):
    """Базовая схема бота."""
    name: str = Field(..., min_length=1, max_length=100, description="Название бота")
    description: Optional[str] = Field(None, max_length=500, description="Описание бота")
    symbol: str = Field(..., description="Торговая пара (например, BTC/USDT)")
    lower_price: float = Field(..., gt=0, description="Нижняя граница сетки")
    upper_price: float = Field(..., gt=0, description="Верхняя граница сетки")
    grid_count: int = Field(..., ge=2, le=100, description="Количество уровней сетки")
    order_amount: float = Field(..., gt=0, description="Размер ордера")
    profit_percentage: float = Field(1.0, ge=0.1, le=10.0, description="Процент прибыли")

    @validator('upper_price')
    def validate_price_range(cls, v, values):
        """Проверяем, что верхняя граница больше нижней."""
        if 'lower_price' in values and v <= values['lower_price']:
            raise ValueError('Верхняя граница должна быть больше нижней')
        return v

    @validator('symbol')
    def validate_symbol(cls, v):
        """Проверяем формат торговой пары."""
        if '/' not in v:
            raise ValueError('Символ должен быть в формате BASE/QUOTE (например, BTC/USDT)')
        return v.upper()


class BotCreate(BotBase):
    """Схема для создания бота."""
    pass


class BotUpdate(BaseModel):
    """Схема для обновления бота."""
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    symbol: Optional[str] = None
    lower_price: Optional[float] = Field(None, gt=0)
    upper_price: Optional[float] = Field(None, gt=0)
    grid_count: Optional[int] = Field(None, ge=2, le=100)
    order_amount: Optional[float] = Field(None, gt=0)
    profit_percentage: Optional[float] = Field(None, ge=0.1, le=10.0)

    @validator('symbol')
    def validate_symbol(cls, v):
        """Проверяем формат торговой пары."""
        if v and '/' not in v:
            raise ValueError('Символ должен быть в формате BASE/QUOTE (например, BTC/USDT)')
        return v.upper() if v else v


class BotResponse(BotBase):
    """Схема ответа с информацией о боте."""
    id: int
    is_active: bool
    total_profit: float
    total_trades: int
    created_at: datetime
    updated_at: Optional[datetime]
    started_at: Optional[datetime]
    stopped_at: Optional[datetime]

    class Config:
        from_attributes = True


class BotStats(BaseModel):
    """Схема статистики бота."""
    bot_id: int
    name: str
    symbol: str
    is_active: bool
    total_profit: float
    total_trades: int
    total_orders: int
    filled_orders: int
    grid_levels: List[float]
    price_range: float
    grid_step: float
    created_at: datetime
    started_at: Optional[datetime]
    stopped_at: Optional[datetime]


class GridLevel(BaseModel):
    """Схема уровня сетки."""
    level: int
    price: float
    order_type: str  # 'buy' или 'sell'
    amount: float
    is_filled: bool = False
    order_id: Optional[int] = None


class BotGridInfo(BaseModel):
    """Информация о сетке бота."""
    bot_id: int
    symbol: str
    lower_price: float
    upper_price: float
    grid_count: int
    grid_step: float
    price_range: float
    levels: List[GridLevel]


class OrderInfo(BaseModel):
    """Информация об ордере."""
    id: int
    symbol: str
    order_type: str
    amount: float
    price: float
    status: str
    filled_amount: float
    created_at: datetime
    filled_at: Optional[datetime]


class BotPerformance(BaseModel):
    """Показатели производительности бота."""
    bot_id: int
    total_profit: float
    total_profit_percent: float
    total_trades: int
    win_rate: float
    average_profit_per_trade: float
    max_drawdown: float
    sharpe_ratio: Optional[float]
    running_time_hours: float


class BotCreateRequest(BaseModel):
    """Запрос на создание бота с дополнительными параметрами."""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    symbol: str = Field(..., description="Торговая пара")
    
    # Параметры сетки
    lower_price: float = Field(..., gt=0, description="Нижняя граница сетки")
    upper_price: float = Field(..., gt=0, description="Верхняя граница сетки")
    grid_count: int = Field(..., ge=2, le=100, description="Количество уровней")
    
    # Управление капиталом
    total_investment: float = Field(..., gt=0, description="Общий размер инвестиций")
    order_amount: Optional[float] = Field(None, gt=0, description="Размер ордера (автоматически если не указан)")
    
    # Настройки прибыли
    profit_percentage: float = Field(1.0, ge=0.1, le=10.0, description="Процент прибыли")
    
    # Дополнительные настройки
    auto_start: bool = Field(False, description="Автоматически запустить после создания")

    @validator('upper_price')
    def validate_price_range(cls, v, values):
        if 'lower_price' in values and v <= values['lower_price']:
            raise ValueError('Верхняя граница должна быть больше нижней')
        return v

    @validator('order_amount', always=True)
    def calculate_order_amount(cls, v, values):
        """Автоматически рассчитываем размер ордера если не указан."""
        if v is None and 'total_investment' in values and 'grid_count' in values:
            # Распределяем инвестиции равномерно по уровням сетки
            return values['total_investment'] / values['grid_count']
        return v


class BotListResponse(BaseModel):
    """Ответ со списком ботов."""
    bots: List[BotResponse]
    total: int
    page: int
    per_page: int
    has_next: bool
    has_prev: bool
