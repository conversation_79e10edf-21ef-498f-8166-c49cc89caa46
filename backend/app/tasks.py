"""
Celery tasks for the trading bot application.
"""

import logging
from typing import List
from celery import current_app as celery_app
from sqlalchemy.orm import Session

from app.database import get_db
from app.models.bot import GridBot
from app.models.order import Order
from app.services.trading_engine import TradingEngine
from app.services.market_data import MarketDataService

logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def check_grid_bots(self):
    """
    Проверяет все активные сеточные боты и выполняет торговые операции.
    """
    try:
        db: Session = next(get_db())
        
        # Получаем все активные боты
        active_bots = db.query(GridBot).filter(GridBot.is_active == True).all()
        
        logger.info(f"Проверяем {len(active_bots)} активных ботов")
        
        for bot in active_bots:
            try:
                # Создаем экземпляр торгового движка для бота
                trading_engine = TradingEngine(bot)
                
                # Выполняем проверку и торговые операции
                trading_engine.process_bot()
                
                logger.info(f"Бот {bot.name} обработан успешно")
                
            except Exception as e:
                logger.error(f"Ошибка при обработке бота {bot.name}: {str(e)}")
                continue
        
        db.close()
        return f"Обработано {len(active_bots)} ботов"
        
    except Exception as e:
        logger.error(f"Ошибка в задаче check_grid_bots: {str(e)}")
        raise self.retry(countdown=60, max_retries=3)


@celery_app.task(bind=True)
def update_market_data(self):
    """
    Обновляет рыночные данные для всех торговых пар.
    """
    try:
        db: Session = next(get_db())
        
        # Получаем все уникальные торговые пары из активных ботов
        trading_pairs = db.query(GridBot.symbol).filter(
            GridBot.is_active == True
        ).distinct().all()
        
        market_service = MarketDataService()
        
        for pair_tuple in trading_pairs:
            pair = pair_tuple[0]
            try:
                # Обновляем данные для торговой пары
                market_service.update_pair_data(pair)
                logger.info(f"Данные для {pair} обновлены")
                
            except Exception as e:
                logger.error(f"Ошибка при обновлении данных для {pair}: {str(e)}")
                continue
        
        db.close()
        return f"Обновлены данные для {len(trading_pairs)} торговых пар"
        
    except Exception as e:
        logger.error(f"Ошибка в задаче update_market_data: {str(e)}")
        raise self.retry(countdown=60, max_retries=3)


@celery_app.task(bind=True)
def execute_order(self, order_id: int):
    """
    Выполняет конкретный ордер.
    """
    try:
        db: Session = next(get_db())
        
        order = db.query(Order).filter(Order.id == order_id).first()
        if not order:
            logger.error(f"Ордер {order_id} не найден")
            return f"Ордер {order_id} не найден"
        
        # Создаем экземпляр торгового движка
        trading_engine = TradingEngine(order.bot)
        
        # Выполняем ордер
        result = trading_engine.execute_order(order)
        
        db.close()
        return f"Ордер {order_id} выполнен: {result}"
        
    except Exception as e:
        logger.error(f"Ошибка при выполнении ордера {order_id}: {str(e)}")
        raise self.retry(countdown=30, max_retries=3)


@celery_app.task
def test_task():
    """
    Тестовая задача для проверки работы Celery.
    """
    logger.info("Тестовая задача выполнена успешно")
    return "Тестовая задача выполнена"
