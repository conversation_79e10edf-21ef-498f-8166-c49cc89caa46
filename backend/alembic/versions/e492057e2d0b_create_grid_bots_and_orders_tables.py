"""Create grid_bots and orders tables

Revision ID: e492057e2d0b
Revises: 
Create Date: 2025-06-01 20:05:39.802621

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e492057e2d0b'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('grid_bots',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('lower_price', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('upper_price', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('grid_count', sa.Integer(), nullable=False),
    sa.Column('order_amount', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('profit_percentage', sa.Numeric(precision=5, scale=2), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('total_profit', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('total_trades', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('stopped_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_grid_bots_id'), 'grid_bots', ['id'], unique=False)
    op.create_index(op.f('ix_grid_bots_name'), 'grid_bots', ['name'], unique=False)
    op.create_index(op.f('ix_grid_bots_symbol'), 'grid_bots', ['symbol'], unique=False)
    op.create_table('orders',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('bot_id', sa.Integer(), nullable=False),
    sa.Column('symbol', sa.String(length=20), nullable=False),
    sa.Column('order_type', sa.Enum('BUY', 'SELL', name='ordertype'), nullable=False),
    sa.Column('amount', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('price', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'FILLED', 'CANCELLED', 'FAILED', 'COMPLETED', name='orderstatus'), nullable=False),
    sa.Column('filled_amount', sa.Numeric(precision=20, scale=8), nullable=False),
    sa.Column('filled_price', sa.Numeric(precision=20, scale=8), nullable=True),
    sa.Column('exchange_order_id', sa.String(length=100), nullable=True),
    sa.Column('parent_order_id', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('filled_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['bot_id'], ['grid_bots.id'], ),
    sa.ForeignKeyConstraint(['parent_order_id'], ['orders.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_orders_bot_id'), 'orders', ['bot_id'], unique=False)
    op.create_index(op.f('ix_orders_exchange_order_id'), 'orders', ['exchange_order_id'], unique=False)
    op.create_index(op.f('ix_orders_id'), 'orders', ['id'], unique=False)
    op.create_index(op.f('ix_orders_status'), 'orders', ['status'], unique=False)
    op.create_index(op.f('ix_orders_symbol'), 'orders', ['symbol'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_orders_symbol'), table_name='orders')
    op.drop_index(op.f('ix_orders_status'), table_name='orders')
    op.drop_index(op.f('ix_orders_id'), table_name='orders')
    op.drop_index(op.f('ix_orders_exchange_order_id'), table_name='orders')
    op.drop_index(op.f('ix_orders_bot_id'), table_name='orders')
    op.drop_table('orders')
    op.drop_index(op.f('ix_grid_bots_symbol'), table_name='grid_bots')
    op.drop_index(op.f('ix_grid_bots_name'), table_name='grid_bots')
    op.drop_index(op.f('ix_grid_bots_id'), table_name='grid_bots')
    op.drop_table('grid_bots')
    # ### end Alembic commands ###
