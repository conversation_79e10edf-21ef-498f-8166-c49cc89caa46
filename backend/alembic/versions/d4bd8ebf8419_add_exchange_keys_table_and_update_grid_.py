"""Add exchange_keys table and update grid_bots

Revision ID: d4bd8ebf8419
Revises: e492057e2d0b
Create Date: 2025-06-01 20:14:45.591523

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd4bd8ebf8419'
down_revision = 'e492057e2d0b'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('exchange_keys',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='Название ключа'),
    sa.Column('exchange', sa.Enum('BINANCE', 'BYBIT', 'OKX', 'KUCOIN', 'GATE', 'HUOBI', 'BITGET', name='exchangetype'), nullable=False, comment='Тип биржи'),
    sa.Column('network', sa.Enum('LIVE', 'TESTNET', name='networktype'), nullable=False, comment='Тип сети'),
    sa.Column('api_key_encrypted', sa.Text(), nullable=False, comment='Зашифрованный API ключ'),
    sa.Column('secret_encrypted', sa.Text(), nullable=False, comment='Зашифрованный секретный ключ'),
    sa.Column('passphrase_encrypted', sa.Text(), nullable=True, comment='Зашифрованная фраза (для некоторых бирж)'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='Активен ли ключ'),
    sa.Column('is_validated', sa.Boolean(), nullable=True, comment='Прошел ли валидацию'),
    sa.Column('last_validation', sa.DateTime(), nullable=True, comment='Последняя проверка'),
    sa.Column('validation_error', sa.Text(), nullable=True, comment='Ошибка валидации'),
    sa.Column('balance_cache', sa.Text(), nullable=True, comment='Кэш баланса в JSON'),
    sa.Column('balance_updated', sa.DateTime(), nullable=True, comment='Время обновления баланса'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='Время создания'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='Время обновления'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_exchange_keys_id'), 'exchange_keys', ['id'], unique=False)
    op.add_column('grid_bots', sa.Column('exchange_key_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'grid_bots', 'exchange_keys', ['exchange_key_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'grid_bots', type_='foreignkey')
    op.drop_column('grid_bots', 'exchange_key_id')
    op.drop_index(op.f('ix_exchange_keys_id'), table_name='exchange_keys')
    op.drop_table('exchange_keys')
    # ### end Alembic commands ###
