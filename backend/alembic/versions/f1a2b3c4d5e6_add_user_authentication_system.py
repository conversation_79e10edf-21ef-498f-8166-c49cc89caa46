"""Add user authentication system

Revision ID: f1a2b3c4d5e6
Revises: d4bd8ebf8419
Create Date: 2025-06-02 14:20:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f1a2b3c4d5e6'
down_revision = 'd4bd8ebf8419'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Создаем таблицу пользователей
    op.create_table('users',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('username', sa.String(length=50), nullable=False),
        sa.Column('email', sa.String(length=255), nullable=False),
        sa.Column('full_name', sa.String(length=100), nullable=True),
        sa.Column('hashed_password', sa.String(length=255), nullable=False),
        sa.Column('is_email_verified', sa.<PERSON>(), nullable=False),
        sa.Column('role', sa.<PERSON>('USER', 'ADMIN', 'MODERATOR', name='userrole'), nullable=False),
        sa.Column('status', sa.Enum('PENDING', 'ACTIVE', 'SUSPENDED', 'DELETED', name='userstatus'), nullable=False),
        sa.Column('otp_secret', sa.String(length=32), nullable=True),
        sa.Column('is_otp_enabled', sa.Boolean(), nullable=False),
        sa.Column('backup_codes', sa.Text(), nullable=True),
        sa.Column('email_verification_token', sa.String(length=255), nullable=True),
        sa.Column('password_reset_token', sa.String(length=255), nullable=True),
        sa.Column('password_reset_expires', sa.DateTime(timezone=True), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_login', sa.DateTime(timezone=True), nullable=True),
        sa.Column('email_verified_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    
    # Добавляем user_id в exchange_keys
    op.add_column('exchange_keys', sa.Column('user_id', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_exchange_keys_user_id'), 'exchange_keys', ['user_id'], unique=False)
    op.create_foreign_key('fk_exchange_keys_user_id', 'exchange_keys', 'users', ['user_id'], ['id'])
    
    # Добавляем user_id в grid_bots
    op.add_column('grid_bots', sa.Column('user_id', sa.Integer(), nullable=True))
    op.create_index(op.f('ix_grid_bots_user_id'), 'grid_bots', ['user_id'], unique=False)
    op.create_foreign_key('fk_grid_bots_user_id', 'grid_bots', 'users', ['user_id'], ['id'])
    
    # Создаем администратора по умолчанию
    from sqlalchemy import text
    op.execute(text("""
        INSERT INTO users (username, email, full_name, hashed_password, is_email_verified, role, status, is_otp_enabled)
        VALUES ('admin', '<EMAIL>', 'Администратор', '$2b$12$LQv3c1yqBWVHxkd0LQ4YCOdHrADfEqJpb4/vUIB5wjTEFkbqQQF9O', true, 'ADMIN', 'ACTIVE', false)
    """))
    
    # Обновляем существующие записи, устанавливая user_id = 1 (admin)
    op.execute(text("UPDATE exchange_keys SET user_id = 1 WHERE user_id IS NULL"))
    op.execute(text("UPDATE grid_bots SET user_id = 1 WHERE user_id IS NULL"))
    
    # Делаем user_id обязательным
    op.alter_column('exchange_keys', 'user_id', nullable=False)
    op.alter_column('grid_bots', 'user_id', nullable=False)
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Удаляем foreign keys и колонки user_id
    op.drop_constraint('fk_grid_bots_user_id', 'grid_bots', type_='foreignkey')
    op.drop_index(op.f('ix_grid_bots_user_id'), table_name='grid_bots')
    op.drop_column('grid_bots', 'user_id')
    
    op.drop_constraint('fk_exchange_keys_user_id', 'exchange_keys', type_='foreignkey')
    op.drop_index(op.f('ix_exchange_keys_user_id'), table_name='exchange_keys')
    op.drop_column('exchange_keys', 'user_id')
    
    # Удаляем таблицу пользователей
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_table('users')
    
    # Удаляем enum типы
    op.execute('DROP TYPE IF EXISTS userrole')
    op.execute('DROP TYPE IF EXISTS userstatus')
    
    # ### end Alembic commands ###
