# Основные настройки
PROJECT_NAME=earnlyze.me
VERSION=1.0.0
DEBUG=false

# База данных
DATABASE_URL=*********************************************/gridbot_db

# Redis
REDIS_URL=redis://redis:6379

# Безопасность
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS
CORS_ORIGINS=http://localhost:3000,https://earnlyze.me

# Email настройки
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Earnlyze

# URL приложения
BASE_URL=http://localhost:3000

# Telegram
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_WEBHOOK_URL=https://your-domain.com/webhook

# Биржи (примеры)
BINANCE_API_KEY=your-binance-api-key
BINANCE_SECRET=your-binance-secret
BINANCE_SANDBOX=true

BYBIT_API_KEY=kRaLlLTats7oz2oftU
BYBIT_SECRET=7iwpbcRrbbV7lBGYpo797IF25xyOrFpZFl83
BYBIT_SANDBOX=true

# Настройки приложения
DEBUG=true
LOG_LEVEL=INFO
CORS_ORIGINS=["http://localhost:3000"]

# Celery
CELERY_BROKER_URL=redis://localhost:6379
CELERY_RESULT_BACKEND=redis://localhost:6379
