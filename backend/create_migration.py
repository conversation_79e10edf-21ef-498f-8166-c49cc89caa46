"""
Скрипт для создания миграций БД
"""
import os
import sys
from alembic.config import Config
from alembic import command

def create_migration(message="Auto migration"):
    """Создать новую миграцию"""
    # Настройка Alembic
    alembic_cfg = Config("alembic.ini")
    
    # Создать миграцию
    command.revision(alembic_cfg, autogenerate=True, message=message)
    print(f"Миграция '{message}' создана успешно")

def upgrade_db():
    """Применить миграции"""
    alembic_cfg = Config("alembic.ini")
    command.upgrade(alembic_cfg, "head")
    print("Миграции применены успешно")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "upgrade":
            upgrade_db()
        else:
            create_migration(sys.argv[1])
    else:
        create_migration("Initial migration")
