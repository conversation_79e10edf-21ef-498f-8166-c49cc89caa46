version: '3.8'

services:
  # PostgreSQL база данных
  postgres:
    image: postgres:15-alpine
    container_name: gridbot_postgres
    environment:
      POSTGRES_DB: gridbot
      POSTGRES_USER: gridbot_user
      POSTGRES_PASSWORD: gridbot_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U gridbot_user -d gridbot"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis для кэширования и очередей
  redis:
    image: redis:7-alpine
    container_name: gridbot_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Backend API
  backend:
    build: ./backend
    container_name: gridbot_backend
    environment:
      - DATABASE_URL=********************************************************/gridbot
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-secret-key-change-in-production
      - DEBUG=true
      - LOG_LEVEL=INFO
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./backend:/app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery Worker для фоновых задач
  celery_worker:
    build: ./backend
    container_name: gridbot_celery_worker
    command: celery -A app.celery worker --loglevel=info
    environment:
      - DATABASE_URL=********************************************************/gridbot
      - REDIS_URL=redis://redis:6379
      - CELERY_BROKER_URL=redis://redis:6379
      - CELERY_RESULT_BACKEND=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    restart: unless-stopped

  # Celery Beat для периодических задач
  celery_beat:
    build: ./backend
    container_name: gridbot_celery_beat
    command: celery -A app.celery beat --loglevel=info
    environment:
      - DATABASE_URL=********************************************************/gridbot
      - REDIS_URL=redis://redis:6379
      - CELERY_BROKER_URL=redis://redis:6379
      - CELERY_RESULT_BACKEND=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
    restart: unless-stopped

  # Frontend React приложение
  frontend:
    image: node:18-alpine
    container_name: gridbot_frontend
    working_dir: /app
    command: sh -c "npm install && npm start"
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  default:
    name: gridbot_network
