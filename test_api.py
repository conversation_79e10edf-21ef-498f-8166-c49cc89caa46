"""
Простой тест API
"""
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_health():
    """Тест health check"""
    try:
        response = requests.get("http://localhost:8000/health")
        print(f"Health check: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Ошибка health check: {e}")
        return False

def test_exchanges():
    """Тест API бирж"""
    try:
        response = requests.get(f"{BASE_URL}/exchanges/")
        print(f"Exchanges: {response.status_code}")
        print(f"Response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"Ошибка exchanges: {e}")
        return False

def test_create_strategy():
    """Тест создания стратегии"""
    strategy_data = {
        "name": "Test BTC Grid Strategy",
        "description": "Тестовая стратегия для BTC",
        "exchange": "binance",
        "symbol": "BTC/USDT:USDT",
        "direction": "long",
        "grid_count": 5,
        "grid_step_percent": 1.0,
        "volume_multiplier": 1.5,
        "deposit_amount": 100.0,
        "leverage": 2.0,
        "tp_percent": 3.0,
        "sl_percent": 5.0,
        "max_drawdown_percent": 10.0
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/strategies/",
            json=strategy_data,
            headers={"Content-Type": "application/json"}
        )
        print(f"Create strategy: {response.status_code}")
        if response.status_code == 200:
            strategy = response.json()
            print(f"Created strategy ID: {strategy['id']}")
            return strategy['id']
        else:
            print(f"Error: {response.text}")
            return None
    except Exception as e:
        print(f"Ошибка создания стратегии: {e}")
        return None

def test_get_strategies():
    """Тест получения стратегий"""
    try:
        response = requests.get(f"{BASE_URL}/strategies/")
        print(f"Get strategies: {response.status_code}")
        if response.status_code == 200:
            strategies = response.json()
            print(f"Found {len(strategies)} strategies")
            return strategies
        else:
            print(f"Error: {response.text}")
            return []
    except Exception as e:
        print(f"Ошибка получения стратегий: {e}")
        return []

def main():
    """Основная функция тестирования"""
    print("🧪 Тестирование Grid Trading Bot API")
    print("=" * 50)
    
    # Тест health check
    print("\n1. Тестирование health check...")
    if not test_health():
        print("❌ Сервер не отвечает")
        return
    
    # Тест API бирж
    print("\n2. Тестирование API бирж...")
    test_exchanges()
    
    # Тест получения стратегий
    print("\n3. Тестирование получения стратегий...")
    strategies = test_get_strategies()
    
    # Тест создания стратегии
    print("\n4. Тестирование создания стратегии...")
    strategy_id = test_create_strategy()
    
    if strategy_id:
        print(f"\n✅ Стратегия создана успешно: {strategy_id}")
        
        # Тест получения конкретной стратегии
        print("\n5. Тестирование получения стратегии по ID...")
        try:
            response = requests.get(f"{BASE_URL}/strategies/{strategy_id}")
            print(f"Get strategy by ID: {response.status_code}")
            if response.status_code == 200:
                strategy = response.json()
                print(f"Strategy name: {strategy['name']}")
                print(f"Strategy status: {strategy['status']}")
        except Exception as e:
            print(f"Ошибка получения стратегии: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Тестирование завершено!")

if __name__ == "__main__":
    main()
