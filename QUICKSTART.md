# 🚀 Быстрый запуск Grid Trading Bot

## 📋 Что уже реализовано

✅ **Backend API** - Полнофункциональный FastAPI сервер  
✅ **Торговый движок** - Система управления стратегиями  
✅ **Алгоритм сетки** - Реализация сеточной торговли  
✅ **CCXT интеграция** - Поддержка Binance и Bybit  
✅ **База данных** - PostgreSQL с моделями  
✅ **Docker** - Контейнеризация всех сервисов  
✅ **Frontend заглушка** - Простой React интерфейс  
✅ **Telegram заглушка** - Базовый бот для уведомлений  

## 🛠️ Установка и запуск

### Вариант 1: Docker (рекомендуется)

1. **Клонируйте проект:**
```bash
git clone <your-repo>
cd trading-service
```

2. **Настройте окружение:**
```bash
cp backend/.env.example backend/.env
```

3. **Отредактируйте `.env` файл:**
```bash
# Добавьте ваши API ключи бирж
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET=your_binance_secret
BINANCE_SANDBOX=true  # true для тестирования

BYBIT_API_KEY=your_bybit_api_key
BYBIT_SECRET=your_bybit_secret
BYBIT_SANDBOX=true

# Опционально: Telegram бот
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
```

4. **Запустите сервисы:**
```bash
docker-compose up -d
```

5. **Проверьте статус:**
```bash
docker-compose ps
```

6. **Откройте приложение:**
- API документация: http://localhost:8000/api/v1/docs
- Frontend: http://localhost:3000
- Health check: http://localhost:8000/health

### Вариант 2: Локальная разработка

1. **Установите зависимости:**
```bash
cd backend
pip install -r requirements.txt
```

2. **Запустите PostgreSQL и Redis локально**

3. **Создайте БД и примените миграции:**
```bash
python create_migration.py upgrade
```

4. **Запустите backend:**
```bash
python run.py
```

5. **В другом терминале запустите frontend:**
```bash
cd frontend
npm install
npm start
```

## 🧪 Тестирование

### Автоматический тест API:
```bash
python test_api.py
```

### Ручное тестирование:

1. **Создание стратегии:**
```bash
curl -X POST "http://localhost:8000/api/v1/strategies/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test BTC Grid",
    "exchange": "binance",
    "symbol": "BTC/USDT:USDT",
    "direction": "long",
    "grid_count": 5,
    "grid_step_percent": 1.0,
    "deposit_amount": 100,
    "leverage": 2.0,
    "tp_percent": 3.0,
    "sl_percent": 5.0
  }'
```

2. **Получение стратегий:**
```bash
curl "http://localhost:8000/api/v1/strategies/"
```

3. **Проверка бирж:**
```bash
curl "http://localhost:8000/api/v1/exchanges/"
```

## 📊 Основные эндпоинты API

- `GET /health` - Проверка здоровья
- `GET /api/v1/strategies/` - Список стратегий
- `POST /api/v1/strategies/` - Создать стратегию
- `POST /api/v1/strategies/{id}/start` - Запустить стратегию
- `POST /api/v1/strategies/{id}/stop` - Остановить стратегию
- `GET /api/v1/trading/portfolio` - Портфель
- `GET /api/v1/exchanges/` - Поддерживаемые биржи

## 🔧 Архитектура

```
Grid Trading Bot
├── Backend (FastAPI)
│   ├── TradingEngine - Управление стратегиями
│   ├── GridAlgorithm - Алгоритм сеточной торговли
│   ├── ExchangeManager - Интеграция с CCXT
│   ├── RiskManager - Управление рисками
│   └── API Routes - REST эндпоинты
├── Database (PostgreSQL)
│   ├── Users - Пользователи
│   ├── Strategies - Стратегии
│   ├── Orders - Ордера
│   └── Trades - Сделки
├── Frontend (React)
│   └── Простой интерфейс для управления
└── Telegram Bot
    └── Уведомления о торговле
```

## ⚠️ Важные замечания

1. **Безопасность:**
   - Используйте sandbox режим для тестирования
   - Никогда не коммитьте реальные API ключи
   - Начинайте с малых сумм

2. **Риски:**
   - Торговля криптовалютами связана с высокими рисками
   - Тестируйте стратегии на демо-счетах
   - Используйте на свой страх и риск

3. **Разработка:**
   - Код готов для дальнейшего развития
   - Все основные компоненты реализованы
   - Легко добавлять новые функции

## 🎯 Следующие шаги

1. **Тестирование** - Протестируйте на sandbox биржах
2. **Frontend** - Развитие React интерфейса
3. **Telegram** - Полная интеграция бота
4. **Аутентификация** - Система пользователей
5. **Мониторинг** - Логи и метрики
6. **Деплой** - Настройка production окружения

## 🆘 Поддержка

Если возникли проблемы:

1. Проверьте логи: `docker-compose logs backend`
2. Проверьте статус: `docker-compose ps`
3. Перезапустите: `docker-compose restart`
4. Пересоберите: `docker-compose up --build`

---

**Поздравляем! 🎉 Ваш Grid Trading Bot готов к работе!**
