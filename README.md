# 🤖 Grid Trading Bot

Бот для сеточной торговли фьючерсами с веб-интерфейсом и Telegram уведомлениями.

## 🚀 Быстрый старт

### Запуск с Docker (рекомендуется)

1. Клонируйте репозиторий:
```bash
git clone <repository-url>
cd trading-service
```

2. Создайте файл окружения:
```bash
cp backend/.env.example backend/.env
```

3. Отредактируйте `.env` файл, добавив ваши API ключи бирж

4. Запустите сервисы:
```bash
docker-compose up -d
```

5. Создайте миграции БД:
```bash
docker-compose exec backend alembic upgrade head
```

6. Откройте API документацию: http://localhost:8000/api/v1/docs

### Запуск для разработки

1. Установите зависимости:
```bash
cd backend
pip install -r requirements.txt
```

2. Настройте PostgreSQL и Redis локально

3. Создайте файл `.env` с настройками БД

4. Запустите миграции:
```bash
alembic upgrade head
```

5. Запустите приложение:
```bash
python run.py
```

## 📋 Функциональность

### ✅ Реализовано

- **Backend API** - FastAPI с полным REST API
- **Торговый движок** - Основной движок для управления стратегиями
- **Алгоритм сетки** - Реализация сеточной торговли
- **Интеграция CCXT** - Поддержка Binance и Bybit
- **Риск-менеджмент** - Система управления рисками
- **База данных** - PostgreSQL с моделями для стратегий, ордеров, сделок
- **API эндпоинты**:
  - `/api/v1/strategies` - Управление стратегиями
  - `/api/v1/trading` - Торговые операции
  - `/api/v1/exchanges` - Работа с биржами

### 🔄 В разработке

- **Frontend** - React веб-интерфейс
- **Telegram Bot** - Уведомления
- **Аутентификация** - Система пользователей
- **WebSocket** - Обновления в реальном времени

## 🏗️ Архитектура

```
trading-service/
├── backend/                 # FastAPI приложение
│   ├── app/
│   │   ├── api/            # REST API эндпоинты
│   │   ├── core/           # Основная логика
│   │   │   ├── trading_engine.py    # Торговый движок
│   │   │   ├── grid_algorithm.py    # Алгоритм сетки
│   │   │   ├── exchange_manager.py  # Менеджер бирж
│   │   │   └── risk_manager.py      # Риск-менеджмент
│   │   ├── models/         # Модели БД
│   │   ├── schemas/        # Pydantic схемы
│   │   └── services/       # Бизнес-логика
│   ├── alembic/           # Миграции БД
│   └── requirements.txt
├── frontend/              # React приложение (планируется)
├── docker-compose.yml     # Docker конфигурация
└── README.md
```

## 🔧 Технологии

- **Backend**: FastAPI, SQLAlchemy, Alembic, CCXT
- **База данных**: PostgreSQL, Redis
- **Торговля**: CCXT (Binance, Bybit)
- **Контейнеризация**: Docker, Docker Compose

## 📊 API Примеры

### Создание стратегии

```bash
curl -X POST "http://localhost:8000/api/v1/strategies/" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "BTC Grid Strategy",
    "exchange": "binance",
    "symbol": "BTC/USDT:USDT",
    "direction": "long",
    "grid_count": 10,
    "grid_step_percent": 1.0,
    "deposit_amount": 1000,
    "leverage": 2.0,
    "tp_percent": 5.0,
    "sl_percent": 10.0
  }'
```

### Запуск стратегии

```bash
curl -X POST "http://localhost:8000/api/v1/strategies/{strategy_id}/start"
```

### Получение портфеля

```bash
curl "http://localhost:8000/api/v1/trading/portfolio"
```

## ⚠️ Важные замечания

1. **Тестирование**: Используйте sandbox режим бирж для тестирования
2. **API ключи**: Никогда не коммитьте реальные API ключи
3. **Риски**: Торговля криптовалютами связана с высокими рисками
4. **Ответственность**: Используйте на свой страх и риск

## 🤝 Вклад в проект

1. Форкните репозиторий
2. Создайте ветку для новой функции
3. Внесите изменения
4. Создайте Pull Request

## 📄 Лицензия

MIT License - см. файл LICENSE для деталей.
