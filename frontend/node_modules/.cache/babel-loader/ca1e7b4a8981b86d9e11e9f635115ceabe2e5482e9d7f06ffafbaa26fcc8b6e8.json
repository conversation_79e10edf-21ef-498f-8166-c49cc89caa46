{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nimport { isUnitless, convertLength, responsiveProperty, alignProperty, fontGrid } from './cssUtils';\nexport default function responsiveFontSizes(themeInput, options = {}) {\n  const {\n    breakpoints = ['sm', 'md', 'lg'],\n    disableAlign = false,\n    factor = 2,\n    variants = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'subtitle1', 'subtitle2', 'body1', 'body2', 'caption', 'button', 'overline']\n  } = options;\n  const theme = _extends({}, themeInput);\n  theme.typography = _extends({}, theme.typography);\n  const typography = theme.typography;\n\n  // Convert between CSS lengths e.g. em->px or px->rem\n  // Set the baseFontSize for your project. Defaults to 16px (also the browser default).\n  const convert = convertLength(typography.htmlFontSize);\n  const breakpointValues = breakpoints.map(x => theme.breakpoints.values[x]);\n  variants.forEach(variant => {\n    const style = typography[variant];\n    if (!style) {\n      return;\n    }\n    const remFontSize = parseFloat(convert(style.fontSize, 'rem'));\n    if (remFontSize <= 1) {\n      return;\n    }\n    const maxFontSize = remFontSize;\n    const minFontSize = 1 + (maxFontSize - 1) / factor;\n    let {\n      lineHeight\n    } = style;\n    if (!isUnitless(lineHeight) && !disableAlign) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: Unsupported non-unitless line height with grid alignment.\nUse unitless line heights instead.` : _formatMuiErrorMessage(6));\n    }\n    if (!isUnitless(lineHeight)) {\n      // make it unitless\n      lineHeight = parseFloat(convert(lineHeight, 'rem')) / parseFloat(remFontSize);\n    }\n    let transform = null;\n    if (!disableAlign) {\n      transform = value => alignProperty({\n        size: value,\n        grid: fontGrid({\n          pixels: 4,\n          lineHeight,\n          htmlFontSize: typography.htmlFontSize\n        })\n      });\n    }\n    typography[variant] = _extends({}, style, responsiveProperty({\n      cssProperty: 'fontSize',\n      min: minFontSize,\n      max: maxFontSize,\n      unit: 'rem',\n      breakpoints: breakpointValues,\n      transform\n    }));\n  });\n  return theme;\n}", "map": {"version": 3, "names": ["_extends", "_formatMuiErrorMessage", "isUnitless", "convertLength", "responsiveProperty", "alignProperty", "fontGrid", "responsiveFontSizes", "themeInput", "options", "breakpoints", "disableAlign", "factor", "variants", "theme", "typography", "convert", "htmlFontSize", "breakpoint<PERSON><PERSON><PERSON>", "map", "x", "values", "for<PERSON>ach", "variant", "style", "remFontSize", "parseFloat", "fontSize", "maxFontSize", "minFontSize", "lineHeight", "Error", "process", "env", "NODE_ENV", "transform", "value", "size", "grid", "pixels", "cssProperty", "min", "max", "unit"], "sources": ["/home/<USER>/projects/trading-service/frontend/node_modules/@mui/material/styles/responsiveFontSizes.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nimport { isUnitless, convertLength, responsiveProperty, alignProperty, fontGrid } from './cssUtils';\nexport default function responsiveFontSizes(themeInput, options = {}) {\n  const {\n    breakpoints = ['sm', 'md', 'lg'],\n    disableAlign = false,\n    factor = 2,\n    variants = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'subtitle1', 'subtitle2', 'body1', 'body2', 'caption', 'button', 'overline']\n  } = options;\n  const theme = _extends({}, themeInput);\n  theme.typography = _extends({}, theme.typography);\n  const typography = theme.typography;\n\n  // Convert between CSS lengths e.g. em->px or px->rem\n  // Set the baseFontSize for your project. Defaults to 16px (also the browser default).\n  const convert = convertLength(typography.htmlFontSize);\n  const breakpointValues = breakpoints.map(x => theme.breakpoints.values[x]);\n  variants.forEach(variant => {\n    const style = typography[variant];\n    if (!style) {\n      return;\n    }\n    const remFontSize = parseFloat(convert(style.fontSize, 'rem'));\n    if (remFontSize <= 1) {\n      return;\n    }\n    const maxFontSize = remFontSize;\n    const minFontSize = 1 + (maxFontSize - 1) / factor;\n    let {\n      lineHeight\n    } = style;\n    if (!isUnitless(lineHeight) && !disableAlign) {\n      throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: Unsupported non-unitless line height with grid alignment.\nUse unitless line heights instead.` : _formatMuiErrorMessage(6));\n    }\n    if (!isUnitless(lineHeight)) {\n      // make it unitless\n      lineHeight = parseFloat(convert(lineHeight, 'rem')) / parseFloat(remFontSize);\n    }\n    let transform = null;\n    if (!disableAlign) {\n      transform = value => alignProperty({\n        size: value,\n        grid: fontGrid({\n          pixels: 4,\n          lineHeight,\n          htmlFontSize: typography.htmlFontSize\n        })\n      });\n    }\n    typography[variant] = _extends({}, style, responsiveProperty({\n      cssProperty: 'fontSize',\n      min: minFontSize,\n      max: maxFontSize,\n      unit: 'rem',\n      breakpoints: breakpointValues,\n      transform\n    }));\n  });\n  return theme;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,SAASC,UAAU,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,YAAY;AACnG,eAAe,SAASC,mBAAmBA,CAACC,UAAU,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;EACpE,MAAM;IACJC,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAChCC,YAAY,GAAG,KAAK;IACpBC,MAAM,GAAG,CAAC;IACVC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU;EAC7H,CAAC,GAAGJ,OAAO;EACX,MAAMK,KAAK,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEQ,UAAU,CAAC;EACtCM,KAAK,CAACC,UAAU,GAAGf,QAAQ,CAAC,CAAC,CAAC,EAAEc,KAAK,CAACC,UAAU,CAAC;EACjD,MAAMA,UAAU,GAAGD,KAAK,CAACC,UAAU;;EAEnC;EACA;EACA,MAAMC,OAAO,GAAGb,aAAa,CAACY,UAAU,CAACE,YAAY,CAAC;EACtD,MAAMC,gBAAgB,GAAGR,WAAW,CAACS,GAAG,CAACC,CAAC,IAAIN,KAAK,CAACJ,WAAW,CAACW,MAAM,CAACD,CAAC,CAAC,CAAC;EAC1EP,QAAQ,CAACS,OAAO,CAACC,OAAO,IAAI;IAC1B,MAAMC,KAAK,GAAGT,UAAU,CAACQ,OAAO,CAAC;IACjC,IAAI,CAACC,KAAK,EAAE;MACV;IACF;IACA,MAAMC,WAAW,GAAGC,UAAU,CAACV,OAAO,CAACQ,KAAK,CAACG,QAAQ,EAAE,KAAK,CAAC,CAAC;IAC9D,IAAIF,WAAW,IAAI,CAAC,EAAE;MACpB;IACF;IACA,MAAMG,WAAW,GAAGH,WAAW;IAC/B,MAAMI,WAAW,GAAG,CAAC,GAAG,CAACD,WAAW,GAAG,CAAC,IAAIhB,MAAM;IAClD,IAAI;MACFkB;IACF,CAAC,GAAGN,KAAK;IACT,IAAI,CAACtB,UAAU,CAAC4B,UAAU,CAAC,IAAI,CAACnB,YAAY,EAAE;MAC5C,MAAM,IAAIoB,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;AAC9D,mCAAmC,GAAGjC,sBAAsB,CAAC,CAAC,CAAC,CAAC;IAC5D;IACA,IAAI,CAACC,UAAU,CAAC4B,UAAU,CAAC,EAAE;MAC3B;MACAA,UAAU,GAAGJ,UAAU,CAACV,OAAO,CAACc,UAAU,EAAE,KAAK,CAAC,CAAC,GAAGJ,UAAU,CAACD,WAAW,CAAC;IAC/E;IACA,IAAIU,SAAS,GAAG,IAAI;IACpB,IAAI,CAACxB,YAAY,EAAE;MACjBwB,SAAS,GAAGC,KAAK,IAAI/B,aAAa,CAAC;QACjCgC,IAAI,EAAED,KAAK;QACXE,IAAI,EAAEhC,QAAQ,CAAC;UACbiC,MAAM,EAAE,CAAC;UACTT,UAAU;UACVb,YAAY,EAAEF,UAAU,CAACE;QAC3B,CAAC;MACH,CAAC,CAAC;IACJ;IACAF,UAAU,CAACQ,OAAO,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAEpB,kBAAkB,CAAC;MAC3DoC,WAAW,EAAE,UAAU;MACvBC,GAAG,EAAEZ,WAAW;MAChBa,GAAG,EAAEd,WAAW;MAChBe,IAAI,EAAE,KAAK;MACXjC,WAAW,EAAEQ,gBAAgB;MAC7BiB;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,OAAOrB,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}