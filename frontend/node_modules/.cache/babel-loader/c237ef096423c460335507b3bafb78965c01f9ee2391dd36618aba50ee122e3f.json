{"ast": null, "code": "export { default } from './useMediaQuery';\nexport * from './useMediaQuery';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/projects/trading-service/frontend/node_modules/@mui/system/esm/useMediaQuery/index.js"], "sourcesContent": ["export { default } from './useMediaQuery';\nexport * from './useMediaQuery';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB;AACzC,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}