{"ast": null, "code": "export { default } from './capitalize';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/projects/trading-service/frontend/node_modules/@mui/utils/esm/capitalize/index.js"], "sourcesContent": ["export { default } from './capitalize';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}