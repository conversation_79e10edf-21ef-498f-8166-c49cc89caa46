{"ast": null, "code": "var _jsxFileName = \"/home/<USER>/projects/trading-service/frontend/src/App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ThemeProvider } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { theme } from './theme';\nimport { Layout } from './components/Layout';\nimport { HomePage } from './pages/HomePage';\nimport { BotsPage } from './pages/BotsPage';\nimport { ExchangeKeysPage } from './pages/ExchangeKeysPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(Layout, {\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/bots\",\n            element: /*#__PURE__*/_jsxDEV(BotsPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 42\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/exchange-keys\",\n            element: /*#__PURE__*/_jsxDEV(ExchangeKeysPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 51\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "ThemeProvider", "CssBaseline", "theme", "Layout", "HomePage", "BotsPage", "ExchangeKeysPage", "jsxDEV", "_jsxDEV", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "_c", "$RefreshReg$"], "sources": ["/home/<USER>/projects/trading-service/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { ThemeProvider } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { theme } from './theme';\nimport { Layout } from './components/Layout';\nimport { HomePage } from './pages/HomePage';\nimport { BotsPage } from './pages/BotsPage';\nimport { ExchangeKeysPage } from './pages/ExchangeKeysPage';\n\nfunction App() {\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Router>\n        <Layout>\n          <Routes>\n            <Route path=\"/\" element={<HomePage />} />\n            <Route path=\"/bots\" element={<BotsPage />} />\n            <Route path=\"/exchange-keys\" element={<ExchangeKeysPage />} />\n          </Routes>\n        </Layout>\n      </Router>\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,aAAa,QAAQ,sBAAsB;AACpD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,gBAAgB,QAAQ,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACR,aAAa;IAACE,KAAK,EAAEA,KAAM;IAAAQ,QAAA,gBAC1BF,OAAA,CAACP,WAAW;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfN,OAAA,CAACX,MAAM;MAAAa,QAAA,eACLF,OAAA,CAACL,MAAM;QAAAO,QAAA,eACLF,OAAA,CAACV,MAAM;UAAAY,QAAA,gBACLF,OAAA,CAACT,KAAK;YAACgB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAER,OAAA,CAACJ,QAAQ;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzCN,OAAA,CAACT,KAAK;YAACgB,IAAI,EAAC,OAAO;YAACC,OAAO,eAAER,OAAA,CAACH,QAAQ;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7CN,OAAA,CAACT,KAAK;YAACgB,IAAI,EAAC,gBAAgB;YAACC,OAAO,eAAER,OAAA,CAACF,gBAAgB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACG,EAAA,GAfQR,GAAG;AAiBZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}