{"ast": null, "code": "'use client';\n\nexport { default } from './useThemeProps';\nexport { default as getThemeProps } from './getThemeProps';", "map": {"version": 3, "names": ["default", "getThemeProps"], "sources": ["/home/<USER>/projects/trading-service/frontend/node_modules/@mui/system/esm/useThemeProps/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './useThemeProps';\nexport { default as getThemeProps } from './getThemeProps';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,iBAAiB;AACzC,SAASA,OAAO,IAAIC,aAAa,QAAQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}