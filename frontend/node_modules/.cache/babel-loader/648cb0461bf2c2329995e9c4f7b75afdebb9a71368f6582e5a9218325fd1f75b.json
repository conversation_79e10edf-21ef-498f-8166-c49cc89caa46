{"ast": null, "code": "'use client';\n\nimport systemUseThemeProps from '@mui/system/useThemeProps';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport default function useThemeProps({\n  props,\n  name\n}) {\n  return systemUseThemeProps({\n    props,\n    name,\n    defaultTheme,\n    themeId: THEME_ID\n  });\n}", "map": {"version": 3, "names": ["systemUseThemeProps", "defaultTheme", "THEME_ID", "useThemeProps", "props", "name", "themeId"], "sources": ["/home/<USER>/projects/trading-service/frontend/node_modules/@mui/material/styles/useThemeProps.js"], "sourcesContent": ["'use client';\n\nimport systemUseThemeProps from '@mui/system/useThemeProps';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport default function useThemeProps({\n  props,\n  name\n}) {\n  return systemUseThemeProps({\n    props,\n    name,\n    defaultTheme,\n    themeId: THEME_ID\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,mBAAmB,MAAM,2BAA2B;AAC3D,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,cAAc;AACnC,eAAe,SAASC,aAAaA,CAAC;EACpCC,KAAK;EACLC;AACF,CAAC,EAAE;EACD,OAAOL,mBAAmB,CAAC;IACzBI,KAAK;IACLC,IAAI;IACJJ,YAAY;IACZK,OAAO,EAAEJ;EACX,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}