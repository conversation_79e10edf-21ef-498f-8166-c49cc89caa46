{"ast": null, "code": "export { default } from './elementAcceptingRef';", "map": {"version": 3, "names": ["default"], "sources": ["/home/<USER>/projects/trading-service/frontend/node_modules/@mui/utils/esm/elementAcceptingRef/index.js"], "sourcesContent": ["export { default } from './elementAcceptingRef';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}