{"ast": null, "code": "/**\n * Extracts event handlers from a given object.\n * A prop is considered an event handler if it is a function and its name starts with `on`.\n *\n * @param object An object to extract event handlers from.\n * @param excludeKeys An array of keys to exclude from the returned object.\n */\nfunction extractEventHandlers(object, excludeKeys = []) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => prop.match(/^on[A-Z]/) && typeof object[prop] === 'function' && !excludeKeys.includes(prop)).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}\nexport default extractEventHandlers;", "map": {"version": 3, "names": ["extractEventHandlers", "object", "excludeKeys", "undefined", "result", "Object", "keys", "filter", "prop", "match", "includes", "for<PERSON>ach"], "sources": ["/home/<USER>/projects/trading-service/frontend/node_modules/@mui/utils/esm/extractEventHandlers/extractEventHandlers.js"], "sourcesContent": ["/**\n * Extracts event handlers from a given object.\n * A prop is considered an event handler if it is a function and its name starts with `on`.\n *\n * @param object An object to extract event handlers from.\n * @param excludeKeys An array of keys to exclude from the returned object.\n */\nfunction extractEventHandlers(object, excludeKeys = []) {\n  if (object === undefined) {\n    return {};\n  }\n  const result = {};\n  Object.keys(object).filter(prop => prop.match(/^on[A-Z]/) && typeof object[prop] === 'function' && !excludeKeys.includes(prop)).forEach(prop => {\n    result[prop] = object[prop];\n  });\n  return result;\n}\nexport default extractEventHandlers;"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,oBAAoBA,CAACC,MAAM,EAAEC,WAAW,GAAG,EAAE,EAAE;EACtD,IAAID,MAAM,KAAKE,SAAS,EAAE;IACxB,OAAO,CAAC,CAAC;EACX;EACA,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjBC,MAAM,CAACC,IAAI,CAACL,MAAM,CAAC,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC,UAAU,CAAC,IAAI,OAAOR,MAAM,CAACO,IAAI,CAAC,KAAK,UAAU,IAAI,CAACN,WAAW,CAACQ,QAAQ,CAACF,IAAI,CAAC,CAAC,CAACG,OAAO,CAACH,IAAI,IAAI;IAC9IJ,MAAM,CAACI,IAAI,CAAC,GAAGP,MAAM,CAACO,IAAI,CAAC;EAC7B,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf;AACA,eAAeJ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}