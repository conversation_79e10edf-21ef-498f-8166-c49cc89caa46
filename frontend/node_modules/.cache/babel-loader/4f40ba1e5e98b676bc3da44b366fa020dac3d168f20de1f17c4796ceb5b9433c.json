{"ast": null, "code": "/**\n * A universal utility to style components with multiple color modes. Always use it from the theme object.\n * It works with:\n *  - [Basic theme](https://mui.com/material-ui/customization/dark-mode/)\n *  - [CSS theme variables](https://mui.com/material-ui/experimental-api/css-theme-variables/overview/)\n *  - Zero-runtime engine\n *\n * Tips: Use an array over object spread and place `theme.applyStyles()` last.\n *\n * ✅ [{ background: '#e5e5e5' }, theme.applyStyles('dark', { background: '#1c1c1c' })]\n *\n * 🚫 { background: '#e5e5e5', ...theme.applyStyles('dark', { background: '#1c1c1c' })}\n *\n * @example\n * 1. using with `styled`:\n * ```jsx\n *   const Component = styled('div')(({ theme }) => [\n *     { background: '#e5e5e5' },\n *     theme.applyStyles('dark', {\n *       background: '#1c1c1c',\n *       color: '#fff',\n *     }),\n *   ]);\n * ```\n *\n * @example\n * 2. using with `sx` prop:\n * ```jsx\n *   <Box sx={theme => [\n *     { background: '#e5e5e5' },\n *     theme.applyStyles('dark', {\n *        background: '#1c1c1c',\n *        color: '#fff',\n *      }),\n *     ]}\n *   />\n * ```\n *\n * @example\n * 3. theming a component:\n * ```jsx\n *   extendTheme({\n *     components: {\n *       MuiButton: {\n *         styleOverrides: {\n *           root: ({ theme }) => [\n *             { background: '#e5e5e5' },\n *             theme.applyStyles('dark', {\n *               background: '#1c1c1c',\n *               color: '#fff',\n *             }),\n *           ],\n *         },\n *       }\n *     }\n *   })\n *```\n */\nexport default function applyStyles(key, styles) {\n  // @ts-expect-error this is 'any' type\n  const theme = this;\n  if (theme.vars && typeof theme.getColorSchemeSelector === 'function') {\n    // If CssVarsProvider is used as a provider,\n    // returns '* :where([data-mui-color-scheme=\"light|dark\"]) &'\n    const selector = theme.getColorSchemeSelector(key).replace(/(\\[[^\\]]+\\])/, '*:where($1)');\n    return {\n      [selector]: styles\n    };\n  }\n  if (theme.palette.mode === key) {\n    return styles;\n  }\n  return {};\n}", "map": {"version": 3, "names": ["applyStyles", "key", "styles", "theme", "vars", "getColorSchemeSelector", "selector", "replace", "palette", "mode"], "sources": ["/home/<USER>/projects/trading-service/frontend/node_modules/@mui/system/esm/createTheme/applyStyles.js"], "sourcesContent": ["/**\n * A universal utility to style components with multiple color modes. Always use it from the theme object.\n * It works with:\n *  - [Basic theme](https://mui.com/material-ui/customization/dark-mode/)\n *  - [CSS theme variables](https://mui.com/material-ui/experimental-api/css-theme-variables/overview/)\n *  - Zero-runtime engine\n *\n * Tips: Use an array over object spread and place `theme.applyStyles()` last.\n *\n * ✅ [{ background: '#e5e5e5' }, theme.applyStyles('dark', { background: '#1c1c1c' })]\n *\n * 🚫 { background: '#e5e5e5', ...theme.applyStyles('dark', { background: '#1c1c1c' })}\n *\n * @example\n * 1. using with `styled`:\n * ```jsx\n *   const Component = styled('div')(({ theme }) => [\n *     { background: '#e5e5e5' },\n *     theme.applyStyles('dark', {\n *       background: '#1c1c1c',\n *       color: '#fff',\n *     }),\n *   ]);\n * ```\n *\n * @example\n * 2. using with `sx` prop:\n * ```jsx\n *   <Box sx={theme => [\n *     { background: '#e5e5e5' },\n *     theme.applyStyles('dark', {\n *        background: '#1c1c1c',\n *        color: '#fff',\n *      }),\n *     ]}\n *   />\n * ```\n *\n * @example\n * 3. theming a component:\n * ```jsx\n *   extendTheme({\n *     components: {\n *       MuiButton: {\n *         styleOverrides: {\n *           root: ({ theme }) => [\n *             { background: '#e5e5e5' },\n *             theme.applyStyles('dark', {\n *               background: '#1c1c1c',\n *               color: '#fff',\n *             }),\n *           ],\n *         },\n *       }\n *     }\n *   })\n *```\n */\nexport default function applyStyles(key, styles) {\n  // @ts-expect-error this is 'any' type\n  const theme = this;\n  if (theme.vars && typeof theme.getColorSchemeSelector === 'function') {\n    // If CssVarsProvider is used as a provider,\n    // returns '* :where([data-mui-color-scheme=\"light|dark\"]) &'\n    const selector = theme.getColorSchemeSelector(key).replace(/(\\[[^\\]]+\\])/, '*:where($1)');\n    return {\n      [selector]: styles\n    };\n  }\n  if (theme.palette.mode === key) {\n    return styles;\n  }\n  return {};\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,WAAWA,CAACC,GAAG,EAAEC,MAAM,EAAE;EAC/C;EACA,MAAMC,KAAK,GAAG,IAAI;EAClB,IAAIA,KAAK,CAACC,IAAI,IAAI,OAAOD,KAAK,CAACE,sBAAsB,KAAK,UAAU,EAAE;IACpE;IACA;IACA,MAAMC,QAAQ,GAAGH,KAAK,CAACE,sBAAsB,CAACJ,GAAG,CAAC,CAACM,OAAO,CAAC,cAAc,EAAE,aAAa,CAAC;IACzF,OAAO;MACL,CAACD,QAAQ,GAAGJ;IACd,CAAC;EACH;EACA,IAAIC,KAAK,CAACK,OAAO,CAACC,IAAI,KAAKR,GAAG,EAAE;IAC9B,OAAOC,MAAM;EACf;EACA,OAAO,CAAC,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}