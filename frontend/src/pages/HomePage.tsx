import React from 'react';
import {
  Container,
  Typography,
  <PERSON>,
  Grid,
  Card,
  CardContent,
  But<PERSON>,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp,
  SmartToy,
  AccountBalance,
  VpnKey,
  Add as AddIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

export const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();

  // Мок данные для демонстрации
  const stats = {
    totalProfit: 1250.45,
    activeBots: 3,
    totalTrades: 127,
    successRate: 78.5,
  };

  const recentBots = [
    {
      id: 1,
      name: 'BTC Grid #1',
      symbol: 'BTC/USDT',
      profit: 245.67,
      status: 'active',
      trades: 23,
    },
    {
      id: 2,
      name: 'ETH Grid #1',
      symbol: 'ETH/USDT',
      profit: 156.78,
      status: 'active',
      trades: 18,
    },
    {
      id: 3,
      name: 'BNB Grid #1',
      symbol: 'BNB/USDT',
      profit: 89.34,
      status: 'stopped',
      trades: 12,
    },
  ];

  return (
    <Container maxWidth="lg">
      <Box py={3}>
        {/* Заголовок */}
        <Box mb={4}>
          <Typography variant="h4" component="h1" gutterBottom>
            Добро пожаловать, {user?.username}! 👋
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Управляйте своими торговыми ботами и отслеживайте прибыль в реальном времени
          </Typography>
        </Box>

        {/* Статистика */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <TrendingUp color="success" sx={{ mr: 1 }} />
                  <Typography variant="h6">Общая прибыль</Typography>
                </Box>
                <Typography variant="h4" color="success.main">
                  ${stats.totalProfit.toFixed(2)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  +12.5% за месяц
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <SmartToy color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">Активные боты</Typography>
                </Box>
                <Typography variant="h4" color="primary.main">
                  {stats.activeBots}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  из 5 созданных
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <AccountBalance color="info" sx={{ mr: 1 }} />
                  <Typography variant="h6">Всего сделок</Typography>
                </Box>
                <Typography variant="h4" color="info.main">
                  {stats.totalTrades}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  за последний месяц
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <VpnKey color="warning" sx={{ mr: 1 }} />
                  <Typography variant="h6">Успешность</Typography>
                </Box>
                <Typography variant="h4" color="warning.main">
                  {stats.successRate}%
                </Typography>
                <Box mt={1}>
                  <LinearProgress
                    variant="determinate"
                    value={stats.successRate}
                    color="warning"
                    sx={{ height: 6, borderRadius: 3 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Быстрые действия */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Быстрые действия
                </Typography>
                <Box display="flex" flexDirection="column" gap={2}>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => navigate('/bots')}
                    fullWidth
                  >
                    Создать нового бота
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<VpnKey />}
                    onClick={() => navigate('/exchange-keys')}
                    fullWidth
                  >
                    Добавить API ключи
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Последние боты
                </Typography>
                <Box display="flex" flexDirection="column" gap={2}>
                  {recentBots.map((bot) => (
                    <Box
                      key={bot.id}
                      display="flex"
                      justifyContent="space-between"
                      alignItems="center"
                      p={2}
                      sx={{
                        backgroundColor: 'background.paper',
                        borderRadius: 1,
                        border: '1px solid',
                        borderColor: 'divider',
                      }}
                    >
                      <Box>
                        <Typography variant="subtitle2">{bot.name}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          {bot.symbol} • {bot.trades} сделок
                        </Typography>
                      </Box>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography
                          variant="body2"
                          color={bot.profit > 0 ? 'success.main' : 'error.main'}
                        >
                          ${bot.profit.toFixed(2)}
                        </Typography>
                        <Chip
                          label={bot.status === 'active' ? 'Активен' : 'Остановлен'}
                          color={bot.status === 'active' ? 'success' : 'default'}
                          size="small"
                        />
                      </Box>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Информационные карточки */}
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="primary">
                  🎯 Сеточная торговля
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Автоматическая торговля с использованием сеточной стратегии.
                  Покупайте дешевле, продавайте дороже.
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="success">
                  🔒 Безопасность
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Ваши API ключи надежно зашифрованы. Мы не имеем доступа к выводу средств.
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom color="info">
                  📊 Аналитика
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Подробная статистика по каждому боту и торговой паре.
                  Отслеживайте прибыль в реальном времени.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );
};
