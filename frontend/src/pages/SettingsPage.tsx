import React, { useState } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Person as PersonIcon,
  Notifications as NotificationsIcon,
  VpnKey as KeyIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

export const SettingsPage: React.FC = () => {
  const { user } = useAuth();
  const [otpEnabled, setOtpEnabled] = useState(user?.is_otp_enabled || false);
  const [setupOtpOpen, setSetupOtpOpen] = useState(false);
  const [qrCode, setQrCode] = useState('');
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [otpToken, setOtpToken] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleSetupOtp = async () => {
    setLoading(true);
    setError('');
    try {
      // Здесь будет вызов API для настройки OTP
      // const response = await authAPI.setupOtp();
      // setQrCode(response.data.qr_code);
      // setBackupCodes(response.data.backup_codes);
      setSetupOtpOpen(true);
      
      // Мок данные для демонстрации
      setQrCode('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
      setBackupCodes(['ABC123', 'DEF456', 'GHI789', 'JKL012', 'MNO345']);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Ошибка при настройке OTP');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async () => {
    setLoading(true);
    setError('');
    try {
      // Здесь будет вызов API для подтверждения OTP
      // await authAPI.verifyOtp({ token: otpToken });
      setOtpEnabled(true);
      setSetupOtpOpen(false);
      setSuccess('OTP успешно настроен!');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Неверный OTP код');
    } finally {
      setLoading(false);
    }
  };

  const handleDisableOtp = async () => {
    setLoading(true);
    setError('');
    try {
      // Здесь будет вызов API для отключения OTP
      // await authAPI.disableOtp({ password: 'user_password', token: 'otp_token' });
      setOtpEnabled(false);
      setSuccess('OTP отключен');
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Ошибка при отключении OTP');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="lg">
      <Typography variant="h4" component="h1" gutterBottom>
        Настройки
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Управляйте настройками безопасности и профиля
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Профиль */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <PersonIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Профиль</Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">
                  Имя пользователя
                </Typography>
                <Typography variant="body1">{user?.username}</Typography>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">
                  Email
                </Typography>
                <Box display="flex" alignItems="center" gap={1}>
                  <Typography variant="body1">{user?.email}</Typography>
                  <Chip
                    label={user?.is_email_verified ? 'Подтвержден' : 'Не подтвержден'}
                    color={user?.is_email_verified ? 'success' : 'warning'}
                    size="small"
                  />
                </Box>
              </Box>
              <Box mb={2}>
                <Typography variant="body2" color="text.secondary">
                  Роль
                </Typography>
                <Typography variant="body1">
                  {user?.role === 'admin' ? 'Администратор' : 'Пользователь'}
                </Typography>
              </Box>
              <Button variant="outlined" disabled>
                Редактировать профиль
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Безопасность */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <SecurityIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Безопасность</Typography>
              </Box>
              
              <Box mb={3}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={otpEnabled}
                      onChange={(e) => {
                        if (e.target.checked) {
                          handleSetupOtp();
                        } else {
                          handleDisableOtp();
                        }
                      }}
                      disabled={loading}
                    />
                  }
                  label="Двухфакторная аутентификация (2FA)"
                />
                <Typography variant="body2" color="text.secondary">
                  Дополнительная защита вашего аккаунта
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Button variant="outlined" disabled sx={{ mb: 2 }}>
                Изменить пароль
              </Button>
              <br />
              <Button variant="outlined" color="error" disabled>
                Удалить аккаунт
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Уведомления */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <NotificationsIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">Уведомления</Typography>
              </Box>
              
              <FormControlLabel
                control={<Switch defaultChecked disabled />}
                label="Email уведомления"
              />
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Получать уведомления о торговых операциях
              </Typography>

              <FormControlLabel
                control={<Switch disabled />}
                label="Telegram уведомления"
              />
              <Typography variant="body2" color="text.secondary">
                Уведомления в Telegram (скоро)
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        {/* API Ключи */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" mb={2}>
                <KeyIcon color="primary" sx={{ mr: 1 }} />
                <Typography variant="h6">API Ключи</Typography>
              </Box>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Управление ключами бирж
              </Typography>

              <Button variant="contained" href="/exchange-keys">
                Управлять ключами
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Диалог настройки OTP */}
      <Dialog open={setupOtpOpen} onClose={() => setSetupOtpOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Настройка двухфакторной аутентификации</DialogTitle>
        <DialogContent>
          <Box textAlign="center" mb={3}>
            <Typography variant="body1" gutterBottom>
              1. Отсканируйте QR код в приложении Google Authenticator
            </Typography>
            {qrCode && (
              <Box mb={2}>
                <img src={qrCode} alt="QR Code" style={{ maxWidth: '200px' }} />
              </Box>
            )}
          </Box>

          <Typography variant="body1" gutterBottom>
            2. Сохраните резервные коды:
          </Typography>
          <Paper sx={{ p: 2, mb: 3, bgcolor: 'background.default' }}>
            <List dense>
              {backupCodes.map((code, index) => (
                <ListItem key={index}>
                  <ListItemText primary={code} />
                </ListItem>
              ))}
            </List>
          </Paper>

          <Typography variant="body1" gutterBottom>
            3. Введите код из приложения для подтверждения:
          </Typography>
          <TextField
            fullWidth
            label="6-значный код"
            value={otpToken}
            onChange={(e) => setOtpToken(e.target.value)}
            placeholder="123456"
            inputProps={{ maxLength: 6 }}
          />

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSetupOtpOpen(false)}>Отмена</Button>
          <Button 
            onClick={handleVerifyOtp} 
            variant="contained" 
            disabled={loading || otpToken.length !== 6}
          >
            Подтвердить
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};
