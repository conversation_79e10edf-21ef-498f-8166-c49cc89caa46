import React, { useState } from 'react';
import {
  Container,
  Typo<PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  TrendingUp,
  SmartToy,
} from '@mui/icons-material';

export const BotsPage: React.FC = () => {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);

  // Мок данные для демонстрации
  const bots = [
    {
      id: 1,
      name: 'BTC Grid Strategy #1',
      symbol: 'BTC/USDT',
      exchange: 'Binance',
      status: 'active',
      profit: 245.67,
      trades: 23,
      gridCount: 10,
      lowerPrice: 42000,
      upperPrice: 48000,
      createdAt: '2024-01-15',
    },
    {
      id: 2,
      name: 'ETH Grid Strategy #1',
      symbol: 'ETH/USDT',
      exchange: 'Binance',
      status: 'active',
      profit: 156.78,
      trades: 18,
      gridCount: 8,
      lowerPrice: 2800,
      upperPrice: 3200,
      createdAt: '2024-01-14',
    },
    {
      id: 3,
      name: 'BNB Grid Strategy #1',
      symbol: 'BNB/USDT',
      exchange: 'Binance',
      status: 'stopped',
      profit: 89.34,
      trades: 12,
      gridCount: 6,
      lowerPrice: 280,
      upperPrice: 320,
      createdAt: '2024-01-13',
    },
  ];

  const activeBots = bots.filter(bot => bot.status === 'active').length;
  const totalProfit = bots.reduce((sum, bot) => sum + bot.profit, 0);

  const handleCreateBot = () => {
    setCreateDialogOpen(true);
  };

  const handleStartBot = (botId: number) => {
    console.log('Starting bot:', botId);
    // Здесь будет логика запуска бота
  };

  const handleStopBot = (botId: number) => {
    console.log('Stopping bot:', botId);
    // Здесь будет логика остановки бота
  };

  const handleDeleteBot = (botId: number) => {
    console.log('Deleting bot:', botId);
    // Здесь будет логика удаления бота
  };

  return (
    <Container maxWidth="lg">
      <Box py={3}>
        {/* Заголовок */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
          <Box>
            <Typography variant="h4" component="h1" gutterBottom>
              Торговые боты
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Создавайте и управляйте сеточными торговыми ботами
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateBot}
            size="large"
          >
            Создать бота
          </Button>
        </Box>

        {/* Статистика */}
        <Grid container spacing={3} mb={4}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <SmartToy color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">Активные боты</Typography>
                </Box>
                <Typography variant="h4" color="primary.main">
                  {activeBots}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  из {bots.length} созданных
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <TrendingUp color="success" sx={{ mr: 1 }} />
                  <Typography variant="h6">Общая прибыль</Typography>
                </Box>
                <Typography variant="h4" color="success.main">
                  ${totalProfit.toFixed(2)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  за все время
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Список ботов */}
        {bots.length === 0 ? (
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 6 }}>
              <SmartToy sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                У вас пока нет торговых ботов
              </Typography>
              <Typography variant="body2" color="text.secondary" mb={3}>
                Создайте своего первого бота для автоматической торговли
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreateBot}
              >
                Создать первого бота
              </Button>
            </CardContent>
          </Card>
        ) : (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Название</TableCell>
                  <TableCell>Пара</TableCell>
                  <TableCell>Биржа</TableCell>
                  <TableCell>Статус</TableCell>
                  <TableCell align="right">Прибыль</TableCell>
                  <TableCell align="right">Сделки</TableCell>
                  <TableCell align="right">Сетка</TableCell>
                  <TableCell align="center">Действия</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {bots.map((bot) => (
                  <TableRow key={bot.id} hover>
                    <TableCell>
                      <Typography variant="subtitle2">{bot.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Создан: {bot.createdAt}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium">
                        {bot.symbol}
                      </Typography>
                    </TableCell>
                    <TableCell>{bot.exchange}</TableCell>
                    <TableCell>
                      <Chip
                        label={bot.status === 'active' ? 'Активен' : 'Остановлен'}
                        color={bot.status === 'active' ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <Typography
                        variant="body2"
                        color={bot.profit > 0 ? 'success.main' : 'error.main'}
                        fontWeight="medium"
                      >
                        ${bot.profit.toFixed(2)}
                      </Typography>
                    </TableCell>
                    <TableCell align="right">{bot.trades}</TableCell>
                    <TableCell align="right">
                      <Typography variant="body2">
                        {bot.gridCount} уровней
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        ${bot.lowerPrice} - ${bot.upperPrice}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Box display="flex" justifyContent="center" gap={1}>
                        {bot.status === 'active' ? (
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleStopBot(bot.id)}
                            title="Остановить"
                          >
                            <StopIcon />
                          </IconButton>
                        ) : (
                          <IconButton
                            size="small"
                            color="success"
                            onClick={() => handleStartBot(bot.id)}
                            title="Запустить"
                          >
                            <PlayIcon />
                          </IconButton>
                        )}
                        <IconButton
                          size="small"
                          color="primary"
                          title="Редактировать"
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleDeleteBot(bot.id)}
                          title="Удалить"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {/* Диалог создания бота */}
        <Dialog
          open={createDialogOpen}
          onClose={() => setCreateDialogOpen(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>Создание нового торгового бота</DialogTitle>
          <DialogContent>
            <Alert severity="info" sx={{ mb: 2 }}>
              Функция создания ботов будет реализована в следующей версии.
              Пока что вы можете просматривать существующих ботов.
            </Alert>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>
              Закрыть
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};
