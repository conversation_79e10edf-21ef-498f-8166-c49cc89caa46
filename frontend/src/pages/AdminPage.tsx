import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Card,
  CardContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Pagination,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  People as PeopleIcon,
  Security as SecurityIcon,
  TrendingUp as TrendingUpIcon,
  SmartToy as BotsIcon,
} from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';

interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  status: string;
  is_email_verified: boolean;
  is_otp_enabled: boolean;
  created_at: string;
}

interface AdminStats {
  total_users: number;
  active_users: number;
  pending_users: number;
  otp_enabled_users: number;
  roles: {
    admin: number;
    user: number;
  };
}

export const AdminPage: React.FC = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [editUser, setEditUser] = useState<User | null>(null);
  const [editOpen, setEditOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Проверяем права доступа
  if (user?.role !== 'admin') {
    return (
      <Container maxWidth="lg">
        <Alert severity="error">
          У вас нет прав доступа к админ панели
        </Alert>
      </Container>
    );
  }

  useEffect(() => {
    loadStats();
    loadUsers();
  }, [page]);

  const loadStats = async () => {
    try {
      // Здесь будет вызов API
      // const response = await adminAPI.getStats();
      // setStats(response.data);
      
      // Мок данные
      setStats({
        total_users: 156,
        active_users: 142,
        pending_users: 14,
        otp_enabled_users: 89,
        roles: {
          admin: 3,
          user: 153
        }
      });
    } catch (err: any) {
      setError('Ошибка при загрузке статистики');
    }
  };

  const loadUsers = async () => {
    setLoading(true);
    try {
      // Здесь будет вызов API
      // const response = await adminAPI.getUsers({ page, per_page: 10 });
      // setUsers(response.data.users);
      // setTotalPages(response.data.pages);
      
      // Мок данные
      setUsers([
        {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          role: 'admin',
          status: 'active',
          is_email_verified: true,
          is_otp_enabled: true,
          created_at: '2024-01-15T10:30:00Z'
        },
        {
          id: 2,
          username: 'testuser2',
          email: '<EMAIL>',
          role: 'user',
          status: 'active',
          is_email_verified: true,
          is_otp_enabled: false,
          created_at: '2024-01-16T14:20:00Z'
        },
        {
          id: 3,
          username: 'newuser',
          email: '<EMAIL>',
          role: 'user',
          status: 'pending',
          is_email_verified: false,
          is_otp_enabled: false,
          created_at: '2024-01-17T09:15:00Z'
        }
      ]);
      setTotalPages(1);
    } catch (err: any) {
      setError('Ошибка при загрузке пользователей');
    } finally {
      setLoading(false);
    }
  };

  const handleEditUser = (user: User) => {
    setEditUser(user);
    setEditOpen(true);
  };

  const handleSaveUser = async () => {
    if (!editUser) return;
    
    try {
      // Здесь будет вызов API
      // await adminAPI.updateUser(editUser.id, editUser);
      
      // Обновляем локальный список
      setUsers(users.map(u => u.id === editUser.id ? editUser : u));
      setEditOpen(false);
      setEditUser(null);
    } catch (err: any) {
      setError('Ошибка при обновлении пользователя');
    }
  };

  const handleDeleteUser = async (userId: number) => {
    if (!window.confirm('Вы уверены, что хотите удалить этого пользователя?')) {
      return;
    }
    
    try {
      // Здесь будет вызов API
      // await adminAPI.deleteUser(userId);
      
      // Обновляем локальный список
      setUsers(users.filter(u => u.id !== userId));
    } catch (err: any) {
      setError('Ошибка при удалении пользователя');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'pending': return 'warning';
      case 'suspended': return 'error';
      default: return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return 'Активен';
      case 'pending': return 'Ожидает';
      case 'suspended': return 'Заблокирован';
      default: return status;
    }
  };

  return (
    <Container maxWidth="lg">
      <Typography variant="h4" component="h1" gutterBottom>
        Админ панель
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Управление пользователями и системой
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Статистика */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={1}>
                  <PeopleIcon color="primary" sx={{ mr: 1 }} />
                  <Typography variant="h6">Всего пользователей</Typography>
                </Box>
                <Typography variant="h4" color="primary.main">
                  {stats.total_users}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={1}>
                  <TrendingUpIcon color="success" sx={{ mr: 1 }} />
                  <Typography variant="h6">Активные</Typography>
                </Box>
                <Typography variant="h4" color="success.main">
                  {stats.active_users}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={1}>
                  <SecurityIcon color="warning" sx={{ mr: 1 }} />
                  <Typography variant="h6">С 2FA</Typography>
                </Box>
                <Typography variant="h4" color="warning.main">
                  {stats.otp_enabled_users}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" mb={1}>
                  <BotsIcon color="info" sx={{ mr: 1 }} />
                  <Typography variant="h6">Админы</Typography>
                </Box>
                <Typography variant="h4" color="info.main">
                  {stats.roles.admin}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Таблица пользователей */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Пользователи
        </Typography>
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Пользователь</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Роль</TableCell>
                <TableCell>Статус</TableCell>
                <TableCell>2FA</TableCell>
                <TableCell>Дата создания</TableCell>
                <TableCell>Действия</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.id}</TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center" gap={1}>
                      {user.email}
                      {user.is_email_verified && (
                        <Chip label="✓" color="success" size="small" />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={user.role === 'admin' ? 'Админ' : 'Пользователь'} 
                      color={user.role === 'admin' ? 'primary' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={getStatusText(user.status)} 
                      color={getStatusColor(user.status) as any}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {user.is_otp_enabled ? '✅' : '❌'}
                  </TableCell>
                  <TableCell>
                    {new Date(user.created_at).toLocaleDateString('ru-RU')}
                  </TableCell>
                  <TableCell>
                    <IconButton 
                      size="small" 
                      onClick={() => handleEditUser(user)}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton 
                      size="small" 
                      onClick={() => handleDeleteUser(user.id)}
                      color="error"
                      disabled={user.id === 1} // Нельзя удалить главного админа
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        <Box display="flex" justifyContent="center" mt={3}>
          <Pagination 
            count={totalPages} 
            page={page} 
            onChange={(e, value) => setPage(value)}
          />
        </Box>
      </Paper>

      {/* Диалог редактирования пользователя */}
      <Dialog open={editOpen} onClose={() => setEditOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Редактировать пользователя</DialogTitle>
        <DialogContent>
          {editUser && (
            <Box display="flex" flexDirection="column" gap={2} mt={1}>
              <TextField
                label="Имя пользователя"
                value={editUser.username}
                onChange={(e) => setEditUser({...editUser, username: e.target.value})}
                fullWidth
              />
              <TextField
                label="Email"
                value={editUser.email}
                onChange={(e) => setEditUser({...editUser, email: e.target.value})}
                fullWidth
              />
              <FormControl fullWidth>
                <InputLabel>Роль</InputLabel>
                <Select
                  value={editUser.role}
                  onChange={(e) => setEditUser({...editUser, role: e.target.value})}
                >
                  <MenuItem value="user">Пользователь</MenuItem>
                  <MenuItem value="admin">Администратор</MenuItem>
                </Select>
              </FormControl>
              <FormControl fullWidth>
                <InputLabel>Статус</InputLabel>
                <Select
                  value={editUser.status}
                  onChange={(e) => setEditUser({...editUser, status: e.target.value})}
                >
                  <MenuItem value="active">Активен</MenuItem>
                  <MenuItem value="pending">Ожидает</MenuItem>
                  <MenuItem value="suspended">Заблокирован</MenuItem>
                </Select>
              </FormControl>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditOpen(false)}>Отмена</Button>
          <Button onClick={handleSaveUser} variant="contained">Сохранить</Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};
