import React, { useState, useEffect } from 'react';
import './App.css';

interface Bot {
  id: string;
  name: string;
  symbol: string;
  is_active: boolean;
  total_profit: number;
  total_trades: number;
}

function App() {
  const [bots, setBots] = useState<Bot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loginForm, setLoginForm] = useState({ username: '', password: '' });

  useEffect(() => {
    // Проверяем, есть ли сохраненная авторизация
    const savedAuth = localStorage.getItem('isAuthenticated');
    if (savedAuth === 'true') {
      setIsAuthenticated(true);
      fetchBots();
    } else {
      setLoading(false);
    }
  }, []);

  const fetchBots = async () => {
    try {
      const response = await fetch('http://localhost:8001/api/v1/bots/');
      if (response.ok) {
        const data = await response.json();
        setBots(data);
      } else {
        setError('Ошибка загрузки ботов');
      }
    } catch (err) {
      setError('Ошибка подключения к серверу');
    } finally {
      setLoading(false);
    }
  };

  const createTestBot = async () => {
    const botData = {
      name: "Test Bot " + Date.now(),
      description: "Тестовый бот",
      symbol: "BTC/USDT",
      lower_price: 40000,
      upper_price: 50000,
      grid_count: 10,
      order_amount: 0.001,
      profit_percentage: 1.0
    };

    try {
      const response = await fetch('http://localhost:8001/api/v1/bots/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(botData),
      });

      if (response.ok) {
        fetchBots(); // Обновить список
      } else {
        setError('Ошибка создания бота');
      }
    } catch (err) {
      setError('Ошибка создания бота');
    }
  };

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (loginForm.username === 'admin' && loginForm.password === '1') {
      setIsAuthenticated(true);
      localStorage.setItem('isAuthenticated', 'true');
      setError(null);
      fetchBots();
    } else {
      setError('Неверный логин или пароль');
    }
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    localStorage.removeItem('isAuthenticated');
    setBots([]);
    setLoginForm({ username: '', password: '' });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLoginForm({
      ...loginForm,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>📈 Earnlyze</h1>
        <p>Платформа крипто ботов</p>
        {isAuthenticated && (
          <button className="button logout-button" onClick={handleLogout}>
            Выйти
          </button>
        )}
      </header>

      <main>
        {error && (
          <div className="error">
            {error}
          </div>
        )}

        {!isAuthenticated ? (
          <div className="login-container">
            <div className="login-form">
              <h2>Вход в систему</h2>
              <form onSubmit={handleLogin}>
                <div className="form-group">
                  <label htmlFor="username">Логин:</label>
                  <input
                    type="text"
                    id="username"
                    name="username"
                    value={loginForm.username}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="password">Пароль:</label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={loginForm.password}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <button type="submit" className="button">
                  Войти
                </button>
              </form>
              <div className="login-hint">
                <small>Логин: admin, Пароль: 1</small>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
              <button className="button" onClick={createTestBot}>
                Создать тестового бота
              </button>
              <button className="button button-outline" onClick={fetchBots}>
                Обновить
              </button>
            </div>

        {loading ? (
          <div className="loading">
            <div>Загрузка...</div>
          </div>
        ) : (
          <div className="grid">
            {bots.length === 0 ? (
              <div className="card">
                <h3>Боты не найдены</h3>
                <p>Создайте первого бота для начала торговли</p>
              </div>
            ) : (
              bots.map((bot) => (
                <div className="card" key={bot.id}>
                  <h3>{bot.name}</h3>
                  <p><strong>Символ:</strong> {bot.symbol}</p>
                  <p><strong>Статус:</strong> {bot.is_active ? 'Активен' : 'Неактивен'}</p>
                  <p><strong>Прибыль:</strong> {bot.total_profit.toFixed(2)} USDT</p>
                  <p><strong>Сделок:</strong> {bot.total_trades}</p>
                </div>
              ))
            )}
          </div>
        )}

        <div style={{ textAlign: 'center', marginTop: '40px' }}>
          <p>
            API Документация: <a href="http://localhost:8001/docs" target="_blank" rel="noopener noreferrer">
              http://localhost:8001/docs
            </a>
          </p>
        </div>
          </>
        )}
      </main>
    </div>
  );
}

export default App;
