import React, { useState, useEffect } from 'react';
import './App.css';

interface Bot {
  id: string;
  name: string;
  symbol: string;
  is_active: boolean;
  total_profit: number;
  total_trades: number;
}

function App() {
  const [bots, setBots] = useState<Bot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchBots();
  }, []);

  const fetchBots = async () => {
    try {
      const response = await fetch('http://localhost:8001/api/v1/bots/');
      if (response.ok) {
        const data = await response.json();
        setBots(data);
      } else {
        setError('Ошибка загрузки ботов');
      }
    } catch (err) {
      setError('Ошибка подключения к серверу');
    } finally {
      setLoading(false);
    }
  };

  const createTestBot = async () => {
    const botData = {
      name: "Test Bot " + Date.now(),
      description: "Тестовый бот",
      symbol: "BTC/USDT",
      lower_price: 40000,
      upper_price: 50000,
      grid_count: 10,
      order_amount: 0.001,
      profit_percentage: 1.0
    };

    try {
      const response = await fetch('http://localhost:8001/api/v1/bots/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(botData),
      });

      if (response.ok) {
        fetchBots(); // Обновить список
      } else {
        setError('Ошибка создания бота');
      }
    } catch (err) {
      setError('Ошибка создания бота');
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>🤖 Grid Trading Bot</h1>
        <p>Бот для сеточной торговли фьючерсами</p>
      </header>

      <main>
        {error && (
          <div className="error">
            {error}
          </div>
        )}

        <div style={{ textAlign: 'center', marginBottom: '20px' }}>
          <button className="button" onClick={createTestBot}>
            Создать тестового бота
          </button>
          <button className="button button-outline" onClick={fetchBots}>
            Обновить
          </button>
        </div>

        {loading ? (
          <div className="loading">
            <div>Загрузка...</div>
          </div>
        ) : (
          <div className="grid">
            {bots.length === 0 ? (
              <div className="card">
                <h3>Боты не найдены</h3>
                <p>Создайте первого бота для начала торговли</p>
              </div>
            ) : (
              bots.map((bot) => (
                <div className="card" key={bot.id}>
                  <h3>{bot.name}</h3>
                  <p><strong>Символ:</strong> {bot.symbol}</p>
                  <p><strong>Статус:</strong> {bot.is_active ? 'Активен' : 'Неактивен'}</p>
                  <p><strong>Прибыль:</strong> {bot.total_profit.toFixed(2)} USDT</p>
                  <p><strong>Сделок:</strong> {bot.total_trades}</p>
                </div>
              ))
            )}
          </div>
        )}

        <div style={{ textAlign: 'center', marginTop: '40px' }}>
          <p>
            API Документация: <a href="http://localhost:8001/docs" target="_blank" rel="noopener noreferrer">
              http://localhost:8001/docs
            </a>
          </p>
        </div>
      </main>
    </div>
  );
}

export default App;
