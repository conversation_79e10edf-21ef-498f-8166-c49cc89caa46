import React, { useState, createContext, useContext, useEffect, ReactNode } from 'react';
import {
  Typo<PERSON>,
  Box,
  ThemeProvider,
  CssBaseline,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  Container,
  AppBar,
  Toolbar,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Menu,
  MenuItem,
  Avatar,
  Divider,
  Grid,
  Paper,
  Chip,
  CircularProgress,
  InputAdornment,
  FormControlLabel,
  Switch,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  Pagination,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Link
} from '@mui/material';
import { createTheme } from '@mui/material/styles';
import { BrowserRouter as Router, Routes, Route, Navigate, useNavigate } from 'react-router-dom';
import {
  Dashboard as DashboardIcon,
  SmartToy as BotsIcon,
  <PERSON>pn<PERSON><PERSON> as KeysIcon,
  <PERSON>u as <PERSON>uIcon,
  ExitToApp as LogoutIcon,
  Settings as SettingsIcon,
  AdminPanelSettings as AdminIcon,
  Visibility,
  VisibilityOff,
  PersonAdd as RegisterIcon,
  Login as LoginIcon,
  Security as SecurityIcon,
  Person as PersonIcon,
  Notifications as NotificationsIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  People as PeopleIcon,
  TrendingUp as TrendingUpIcon
} from '@mui/icons-material';

const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#7C3AED',
      light: '#A855F7',
      dark: '#5B21B6',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#F59E0B',
      light: '#FCD34D',
      dark: '#D97706',
      contrastText: '#000000',
    },
    background: {
      default: '#0F172A',
      paper: '#1E293B',
    },
    text: {
      primary: '#F8FAFC',
      secondary: '#CBD5E1',
    },
  },
});

// Интерфейсы
interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  status: string;
  is_email_verified: boolean;
  is_otp_enabled: boolean;
  full_name?: string;
  created_at: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
}

// Контекст авторизации
const AuthContext = createContext<AuthContextType | undefined>(undefined);

const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const isAuthenticated = !!user;

  useEffect(() => {
    // Проверяем сохраненного пользователя
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      setUser(JSON.parse(savedUser));
    }
    setLoading(false);
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    if (username === 'admin' && password === '1') {
      const mockUser: User = {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
        status: 'active',
        is_email_verified: true,
        is_otp_enabled: false,
        full_name: 'Администратор',
        created_at: new Date().toISOString()
      };
      setUser(mockUser);
      localStorage.setItem('user', JSON.stringify(mockUser));
      return true;
    }
    return false;
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
  };

  return (
    <AuthContext.Provider value={{ user, isAuthenticated, loading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

// Компонент защищенного роута
const ProtectedRoute: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="100vh">
        <CircularProgress />
      </Box>
    );
  }

  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};

// Страница входа
const LoginPage: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const success = await login(username, password);
      if (success) {
        navigate('/');
      } else {
        setError('Неверный логин или пароль');
      }
    } catch (err) {
      setError('Ошибка при входе в систему');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container maxWidth="sm">
      <Box sx={{ mt: 8 }}>
        <Card>
          <CardContent sx={{ p: 4 }}>
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography variant="h3" component="h1" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                Earnlyze
              </Typography>
              <Typography variant="h6" color="text.secondary">
                Платформа крипто ботов
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <Box component="form" onSubmit={handleSubmit}>
              <TextField
                fullWidth
                label="Имя пользователя"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                margin="normal"
                required
                disabled={loading}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PersonIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Пароль"
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                margin="normal"
                required
                disabled={loading}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SecurityIcon />
                    </InputAdornment>
                  ),
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
                sx={{ mb: 3 }}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={loading}
                sx={{ py: 1.5, mb: 2 }}
              >
                {loading ? <CircularProgress size={24} /> : 'Войти'}
              </Button>

              <Button
                fullWidth
                variant="outlined"
                size="large"
                onClick={() => navigate('/register')}
                startIcon={<RegisterIcon />}
                sx={{ py: 1.5 }}
              >
                Регистрация
              </Button>
            </Box>

            <Box sx={{ mt: 3, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary" align="center">
                <strong>Тестовые данные:</strong><br />
                Логин: <Chip label="admin" size="small" /> | Пароль: <Chip label="1" size="small" />
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

// Страница регистрации
const RegisterPage: React.FC = () => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    fullName: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const handleChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, [field]: e.target.value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (formData.password !== formData.confirmPassword) {
      setError('Пароли не совпадают');
      return;
    }

    if (formData.password.length < 6) {
      setError('Пароль должен содержать минимум 6 символов');
      return;
    }

    setLoading(true);
    // Имитация регистрации
    setTimeout(() => {
      setLoading(false);
      alert('Регистрация успешна! Теперь вы можете войти в систему.');
      navigate('/login');
    }, 1000);
  };

  return (
    <Container maxWidth="sm">
      <Box sx={{ mt: 4 }}>
        <Card>
          <CardContent sx={{ p: 4 }}>
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                Регистрация
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Создайте аккаунт в Earnlyze
              </Typography>
            </Box>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <Box component="form" onSubmit={handleSubmit}>
              <TextField
                fullWidth
                label="Полное имя"
                value={formData.fullName}
                onChange={handleChange('fullName')}
                margin="normal"
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Имя пользователя"
                value={formData.username}
                onChange={handleChange('username')}
                margin="normal"
                required
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={handleChange('email')}
                margin="normal"
                required
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Пароль"
                type="password"
                value={formData.password}
                onChange={handleChange('password')}
                margin="normal"
                required
                sx={{ mb: 2 }}
              />

              <TextField
                fullWidth
                label="Подтвердите пароль"
                type="password"
                value={formData.confirmPassword}
                onChange={handleChange('confirmPassword')}
                margin="normal"
                required
                sx={{ mb: 3 }}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={loading}
                sx={{ py: 1.5, mb: 2 }}
              >
                {loading ? <CircularProgress size={24} /> : 'Зарегистрироваться'}
              </Button>

              <Button
                fullWidth
                variant="outlined"
                size="large"
                onClick={() => navigate('/login')}
                startIcon={<LoginIcon />}
                sx={{ py: 1.5 }}
              >
                Уже есть аккаунт? Войти
              </Button>
            </Box>
          </CardContent>
        </Card>
      </Box>
    </Container>
  );
};

// Layout компонент
const Layout: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const menuItems = [
    { text: 'Главная', icon: <DashboardIcon />, path: '/' },
    { text: 'Боты', icon: <BotsIcon />, path: '/bots' },
    { text: 'API Ключи', icon: <KeysIcon />, path: '/exchange-keys' },
    { text: 'Настройки', icon: <SettingsIcon />, path: '/settings' },
    ...(user?.role === 'admin' ? [{ text: 'Админ панель', icon: <AdminIcon />, path: '/admin' }] : [])
  ];

  const handleLogout = () => {
    logout();
    navigate('/login');
    setAnchorEl(null);
  };

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar position="fixed" sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}>
        <Toolbar>
          <IconButton
            color="inherit"
            edge="start"
            onClick={() => setDrawerOpen(!drawerOpen)}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            Earnlyze
          </Typography>

          <IconButton
            color="inherit"
            onClick={(e) => setAnchorEl(e.currentTarget)}
          >
            <Avatar sx={{ bgcolor: 'secondary.main' }}>
              {user?.username?.charAt(0).toUpperCase()}
            </Avatar>
          </IconButton>

          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={() => setAnchorEl(null)}
          >
            <MenuItem onClick={() => { navigate('/settings'); setAnchorEl(null); }}>
              <ListItemIcon><SettingsIcon /></ListItemIcon>
              Настройки
            </MenuItem>
            <Divider />
            <MenuItem onClick={handleLogout}>
              <ListItemIcon><LogoutIcon /></ListItemIcon>
              Выйти
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      <Drawer
        variant="temporary"
        open={drawerOpen}
        onClose={() => setDrawerOpen(false)}
        sx={{
          width: 240,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: 240,
            boxSizing: 'border-box',
          },
        }}
      >
        <Toolbar />
        <Box sx={{ overflow: 'auto' }}>
          <List>
            {menuItems.map((item) => (
              <ListItem
                key={item.text}
                onClick={() => {
                  navigate(item.path);
                  setDrawerOpen(false);
                }}
                sx={{ cursor: 'pointer' }}
              >
                <ListItemIcon>{item.icon}</ListItemIcon>
                <ListItemText primary={item.text} />
              </ListItem>
            ))}
          </List>
        </Box>
      </Drawer>

      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        <Toolbar />
        {children}
      </Box>
    </Box>
  );
};

// Главная страница
const HomePage: React.FC = () => {
  const { user } = useAuth();

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Добро пожаловать, {user?.full_name || user?.username}!
      </Typography>

      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <BotsIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Активные боты
            </Typography>
            <Typography variant="h3" color="primary.main">
              0
            </Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <TrendingUpIcon sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Общая прибыль
            </Typography>
            <Typography variant="h3" color="success.main">
              $0.00
            </Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <KeysIcon sx={{ fontSize: 48, color: 'warning.main', mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              API Ключи
            </Typography>
            <Typography variant="h3" color="warning.main">
              0
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      <Paper sx={{ p: 3, mt: 3 }}>
        <Typography variant="h6" gutterBottom>
          Быстрый старт
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Добро пожаловать в Earnlyze! Для начала работы:
        </Typography>
        <Box component="ol" sx={{ pl: 2 }}>
          <Typography component="li" sx={{ mb: 1 }}>
            Добавьте API ключи от биржи в разделе "API Ключи"
          </Typography>
          <Typography component="li" sx={{ mb: 1 }}>
            Создайте своего первого торгового бота в разделе "Боты"
          </Typography>
          <Typography component="li" sx={{ mb: 1 }}>
            Настройте параметры сетки и запустите бота
          </Typography>
        </Box>
      </Paper>
    </Box>
  );
};

// Страница ботов
const BotsPage: React.FC = () => {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Торговые боты
      </Typography>
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <BotsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          У вас пока нет ботов
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Создайте своего первого торгового бота для автоматической торговли
        </Typography>
        <Button variant="contained" size="large">
          Создать бота
        </Button>
      </Paper>
    </Box>
  );
};

// Страница API ключей
const ExchangeKeysPage: React.FC = () => {
  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        API Ключи бирж
      </Typography>
      <Paper sx={{ p: 3, textAlign: 'center' }}>
        <KeysIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          API ключи не добавлены
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Добавьте API ключи от биржи для работы с торговыми ботами
        </Typography>
        <Button variant="contained" size="large">
          Добавить ключи
        </Button>
      </Paper>
    </Box>
  );
};

// Страница настроек
const SettingsPage: React.FC = () => {
  const { user } = useAuth();
  const [otpEnabled, setOtpEnabled] = useState(user?.is_otp_enabled || false);

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Настройки
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Профиль
            </Typography>
            <TextField
              fullWidth
              label="Полное имя"
              defaultValue={user?.full_name}
              margin="normal"
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Email"
              defaultValue={user?.email}
              margin="normal"
              sx={{ mb: 2 }}
            />
            <Button variant="contained">
              Сохранить изменения
            </Button>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Безопасность
            </Typography>
            <FormControlLabel
              control={
                <Switch
                  checked={otpEnabled}
                  onChange={(e) => setOtpEnabled(e.target.checked)}
                />
              }
              label="Двухфакторная аутентификация (2FA)"
              sx={{ mb: 2 }}
            />
            <Box sx={{ mt: 2 }}>
              <Button variant="outlined" sx={{ mr: 2 }}>
                Изменить пароль
              </Button>
              <Button variant="outlined">
                Резервные коды
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

// Админ панель
const AdminPage: React.FC = () => {
  const mockUsers = [
    { id: 1, username: 'admin', email: '<EMAIL>', role: 'admin', status: 'active' },
    { id: 2, username: 'user1', email: '<EMAIL>', role: 'user', status: 'active' },
    { id: 3, username: 'user2', email: '<EMAIL>', role: 'user', status: 'inactive' },
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Админ панель
      </Typography>

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <PeopleIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
            <Typography variant="h4" color="primary.main">
              {mockUsers.length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Всего пользователей
            </Typography>
          </Paper>
        </Grid>

        <Grid item xs={12} md={3}>
          <Paper sx={{ p: 3, textAlign: 'center' }}>
            <BotsIcon sx={{ fontSize: 48, color: 'success.main', mb: 1 }} />
            <Typography variant="h4" color="success.main">
              0
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Активных ботов
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Управление пользователями
        </Typography>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>ID</TableCell>
                <TableCell>Пользователь</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Роль</TableCell>
                <TableCell>Статус</TableCell>
                <TableCell>Действия</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {mockUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>{user.id}</TableCell>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Chip
                      label={user.role}
                      color={user.role === 'admin' ? 'primary' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={user.status}
                      color={user.status === 'active' ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <IconButton size="small" sx={{ mr: 1 }}>
                      <EditIcon />
                    </IconButton>
                    <IconButton size="small" color="error">
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>
    </Box>
  );
};

const App: React.FC = () => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <Router>
          <Routes>
            {/* Публичные роуты */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />

            {/* Защищенные роуты */}
            <Route path="/" element={
              <ProtectedRoute>
                <Layout>
                  <HomePage />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/bots" element={
              <ProtectedRoute>
                <Layout>
                  <BotsPage />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/exchange-keys" element={
              <ProtectedRoute>
                <Layout>
                  <ExchangeKeysPage />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/settings" element={
              <ProtectedRoute>
                <Layout>
                  <SettingsPage />
                </Layout>
              </ProtectedRoute>
            } />

            <Route path="/admin" element={
              <ProtectedRoute>
                <Layout>
                  <AdminPage />
                </Layout>
              </ProtectedRoute>
            } />

            {/* Перенаправление неизвестных роутов */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;