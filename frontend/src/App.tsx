import React, { useState, useEffect } from 'react';
import './App.css';

interface Bot {
  id: string;
  name: string;
  symbol: string;
  is_active: boolean;
  total_profit: number;
  total_trades: number;
}

function App() {
  const [bots, setBots] = useState<Bot[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loginForm, setLoginForm] = useState({ username: '', password: '' });
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [createBotForm, setCreateBotForm] = useState({
    name: '',
    description: '',
    symbol: 'BTC/USDT',
    lower_price: '',
    upper_price: '',
    grid_count: 10,
    order_amount: '',
    profit_percentage: 1.0
  });

  useEffect(() => {
    // Проверяем, есть ли сохраненная авторизация
    const savedAuth = localStorage.getItem('isAuthenticated');
    if (savedAuth === 'true') {
      setIsAuthenticated(true);
      fetchBots();
    } else {
      setLoading(false);
    }
  }, []);

  const fetchBots = async () => {
    try {
      const response = await fetch('http://localhost:8001/api/v1/bots/');
      if (response.ok) {
        const data = await response.json();
        setBots(data);
      } else {
        setError('Ошибка загрузки ботов');
      }
    } catch (err) {
      setError('Ошибка подключения к серверу');
    } finally {
      setLoading(false);
    }
  };

  const createTestBot = async () => {
    const botData = {
      name: "Test Bot " + Date.now(),
      description: "Тестовый бот",
      symbol: "BTC/USDT",
      lower_price: 40000,
      upper_price: 50000,
      grid_count: 10,
      order_amount: 0.001,
      profit_percentage: 1.0
    };

    try {
      const response = await fetch('http://localhost:8001/api/v1/bots/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(botData),
      });

      if (response.ok) {
        fetchBots(); // Обновить список
      } else {
        setError('Ошибка создания бота');
      }
    } catch (err) {
      setError('Ошибка создания бота');
    }
  };

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (loginForm.username === 'admin' && loginForm.password === '1') {
      setIsAuthenticated(true);
      localStorage.setItem('isAuthenticated', 'true');
      setError(null);
      fetchBots();
    } else {
      setError('Неверный логин или пароль');
    }
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    localStorage.removeItem('isAuthenticated');
    setBots([]);
    setLoginForm({ username: '', password: '' });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLoginForm({
      ...loginForm,
      [e.target.name]: e.target.value
    });
  };

  const handleCreateBotInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setCreateBotForm({
      ...createBotForm,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    });
  };

  const handleCreateBot = async (e: React.FormEvent) => {
    e.preventDefault();

    // Валидация
    if (!createBotForm.name.trim()) {
      setError('Введите название бота');
      return;
    }

    if (!createBotForm.lower_price || !createBotForm.upper_price) {
      setError('Введите границы сетки');
      return;
    }

    if (parseFloat(createBotForm.lower_price) >= parseFloat(createBotForm.upper_price)) {
      setError('Нижняя граница должна быть меньше верхней');
      return;
    }

    if (!createBotForm.order_amount) {
      setError('Введите размер ордера');
      return;
    }

    try {
      const response = await fetch('http://localhost:8001/api/v1/bots/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: createBotForm.name,
          description: createBotForm.description,
          symbol: createBotForm.symbol,
          lower_price: parseFloat(createBotForm.lower_price),
          upper_price: parseFloat(createBotForm.upper_price),
          grid_count: createBotForm.grid_count,
          order_amount: parseFloat(createBotForm.order_amount),
          profit_percentage: createBotForm.profit_percentage
        }),
      });

      if (response.ok) {
        setShowCreateForm(false);
        setCreateBotForm({
          name: '',
          description: '',
          symbol: 'BTC/USDT',
          lower_price: '',
          upper_price: '',
          grid_count: 10,
          order_amount: '',
          profit_percentage: 1.0
        });
        setError(null);
        fetchBots(); // Обновить список
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Ошибка создания бота');
      }
    } catch (err) {
      setError('Ошибка создания бота');
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>📈 Earnlyze</h1>
        <p>Платформа крипто ботов</p>
        {isAuthenticated && (
          <button className="button logout-button" onClick={handleLogout}>
            Выйти
          </button>
        )}
      </header>

      <main>
        {error && (
          <div className="error">
            {error}
          </div>
        )}

        {!isAuthenticated ? (
          <div className="login-container">
            <div className="login-form">
              <h2>Вход в систему</h2>
              <form onSubmit={handleLogin}>
                <div className="form-group">
                  <label htmlFor="username">Логин:</label>
                  <input
                    type="text"
                    id="username"
                    name="username"
                    value={loginForm.username}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="password">Пароль:</label>
                  <input
                    type="password"
                    id="password"
                    name="password"
                    value={loginForm.password}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <button type="submit" className="button">
                  Войти
                </button>
              </form>
              <div className="login-hint">
                <small>Логин: admin, Пароль: 1</small>
              </div>
            </div>
          </div>
        ) : (
          <>
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
              <button className="button" onClick={() => setShowCreateForm(true)}>
                Создать бота
              </button>
              <button className="button button-outline" onClick={createTestBot}>
                Создать тестового бота
              </button>
              <button className="button button-outline" onClick={fetchBots}>
                Обновить
              </button>
            </div>

            {showCreateForm && (
              <div className="create-bot-form">
                <h3>Создать нового бота</h3>
                <form onSubmit={handleCreateBot}>
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="name">Название бота:</label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={createBotForm.name}
                        onChange={handleCreateBotInputChange}
                        required
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="symbol">Торговая пара:</label>
                      <select
                        id="symbol"
                        name="symbol"
                        value={createBotForm.symbol}
                        onChange={handleCreateBotInputChange}
                      >
                        <option value="BTC/USDT">BTC/USDT</option>
                        <option value="ETH/USDT">ETH/USDT</option>
                        <option value="BNB/USDT">BNB/USDT</option>
                        <option value="ADA/USDT">ADA/USDT</option>
                      </select>
                    </div>
                  </div>

                  <div className="form-group">
                    <label htmlFor="description">Описание (необязательно):</label>
                    <textarea
                      id="description"
                      name="description"
                      value={createBotForm.description}
                      onChange={handleCreateBotInputChange}
                      rows={3}
                    />
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="lower_price">Нижняя граница ($):</label>
                      <input
                        type="number"
                        id="lower_price"
                        name="lower_price"
                        value={createBotForm.lower_price}
                        onChange={handleCreateBotInputChange}
                        step="0.01"
                        min="0"
                        required
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="upper_price">Верхняя граница ($):</label>
                      <input
                        type="number"
                        id="upper_price"
                        name="upper_price"
                        value={createBotForm.upper_price}
                        onChange={handleCreateBotInputChange}
                        step="0.01"
                        min="0"
                        required
                      />
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="grid_count">Количество уровней:</label>
                      <input
                        type="number"
                        id="grid_count"
                        name="grid_count"
                        value={createBotForm.grid_count}
                        onChange={handleCreateBotInputChange}
                        min="2"
                        max="100"
                        required
                      />
                    </div>
                    <div className="form-group">
                      <label htmlFor="order_amount">Размер ордера ($):</label>
                      <input
                        type="number"
                        id="order_amount"
                        name="order_amount"
                        value={createBotForm.order_amount}
                        onChange={handleCreateBotInputChange}
                        step="0.01"
                        min="0.01"
                        required
                      />
                    </div>
                  </div>

                  <div className="form-group">
                    <label htmlFor="profit_percentage">Процент прибыли (%):</label>
                    <input
                      type="number"
                      id="profit_percentage"
                      name="profit_percentage"
                      value={createBotForm.profit_percentage}
                      onChange={handleCreateBotInputChange}
                      step="0.1"
                      min="0.1"
                      max="10"
                      required
                    />
                  </div>

                  <div className="form-actions">
                    <button type="submit" className="button">
                      Создать бота
                    </button>
                    <button
                      type="button"
                      className="button button-outline"
                      onClick={() => setShowCreateForm(false)}
                    >
                      Отмена
                    </button>
                  </div>
                </form>
              </div>
            )}

        {loading ? (
          <div className="loading">
            <div>Загрузка...</div>
          </div>
        ) : (
          <div className="grid">
            {bots.length === 0 ? (
              <div className="card">
                <h3>Боты не найдены</h3>
                <p>Создайте первого бота для начала торговли</p>
              </div>
            ) : (
              bots.map((bot) => (
                <div className="card" key={bot.id}>
                  <h3>{bot.name}</h3>
                  <p><strong>Символ:</strong> {bot.symbol}</p>
                  <p><strong>Статус:</strong> {bot.is_active ? 'Активен' : 'Неактивен'}</p>
                  <p><strong>Прибыль:</strong> {bot.total_profit.toFixed(2)} USDT</p>
                  <p><strong>Сделок:</strong> {bot.total_trades}</p>
                </div>
              ))
            )}
          </div>
        )}

        <div style={{ textAlign: 'center', marginTop: '40px' }}>
          <p>
            API Документация: <a href="http://localhost:8001/docs" target="_blank" rel="noopener noreferrer">
              http://localhost:8001/docs
            </a>
          </p>
        </div>
          </>
        )}
      </main>
    </div>
  );
}

export default App;
