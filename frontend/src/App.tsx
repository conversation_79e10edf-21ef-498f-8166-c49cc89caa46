import React from 'react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Container, Typography, Box, Paper } from '@mui/material';

// Простая темная тема
const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#9c27b0',
    },
    secondary: {
      main: '#ff9800',
    },
    background: {
      default: '#0a0a0a',
      paper: '#1a1a1a',
    },
  },
});

const App: React.FC = () => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box textAlign="center" mb={4}>
          <Typography variant="h2" component="h1" gutterBottom>
            Earnlyze
          </Typography>
          <Typography variant="h5" color="text.secondary">
            Платформа крипто ботов для сеточной торговли
          </Typography>
        </Box>
        
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h4" gutterBottom>
            🚀 Добро пожаловать!
          </Typography>
          <Typography variant="body1" paragraph>
            Платформа успешно запущена и готова к работе.
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Интерфейс в стиле Veles с темной темой активирован.
          </Typography>
        </Paper>
      </Container>
    </ThemeProvider>
  );
};

export default App;
