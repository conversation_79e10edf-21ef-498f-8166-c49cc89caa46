import React, { useState, useEffect, createContext, useContext } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import {
  Box,
  CircularProgress,
  AppBar,
  Toolbar,
  Typography,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Container,
  Paper,
  Button,
  TextField,
  Card,
  CardContent,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Divider,
  Link
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  SmartToy as BotsIcon,
  VpnKey as KeysIcon,
  AccountCircle as AccountIcon,
  Menu as MenuIcon,
  ExitToApp as LogoutIcon
} from '@mui/icons-material';

// Темная тема в стиле Veles
const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#7C3AED',
      light: '#A855F7',
      dark: '#5B21B6',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#F59E0B',
      light: '#FCD34D',
      dark: '#D97706',
      contrastText: '#000000',
    },
    background: {
      default: '#0F172A',
      paper: '#1E293B',
    },
    text: {
      primary: '#F8FAFC',
      secondary: '#CBD5E1',
    },
  },
  components: {
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#1E293B',
          borderBottom: '1px solid #475569',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#1E293B',
          borderRight: '1px solid #475569',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundColor: '#1E293B',
          border: '1px solid #475569',
          borderRadius: 12,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
        },
      },
    },
  },
});

// Простая система авторизации
interface AuthContextType {
  isAuthenticated: boolean;
  loading: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('token');
    setIsAuthenticated(!!token);
    setLoading(false);
  }, []);

  const login = async (username: string, password: string): Promise<boolean> => {
    if (username === 'admin' && password === '1') {
      localStorage.setItem('token', 'fake-token');
      setIsAuthenticated(true);
      return true;
    }
    return false;
  };

  const logout = () => {
    localStorage.removeItem('token');
    setIsAuthenticated(false);
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, loading, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

// Компонент для защищенных роутов
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: 'background.default',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return isAuthenticated ? <>{children}</> : <Navigate to="/login" replace />;
};

// Модальное окно входа
interface LoginModalProps {
  open: boolean;
  onClose: () => void;
}

const LoginModal: React.FC<LoginModalProps> = ({ open, onClose }) => {
  const [isLogin, setIsLogin] = useState(true);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [email, setEmail] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      if (isLogin) {
        const success = await login(username, password);
        if (!success) {
          setError('Неверный логин или пароль');
        } else {
          onClose();
        }
      } else {
        // Регистрация - пока заглушка
        if (password !== confirmPassword) {
          setError('Пароли не совпадают');
          return;
        }
        setError('Регистрация временно недоступна');
      }
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setUsername('');
    setPassword('');
    setEmail('');
    setConfirmPassword('');
    setError('');
    setLoading(false);
  };

  const handleClose = () => {
    resetForm();
    onClose();
  };

  const toggleMode = () => {
    resetForm();
    setIsLogin(!isLogin);
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="sm" fullWidth>
      <DialogTitle sx={{ textAlign: 'center', pb: 1 }}>
        <Typography variant="h4" component="h2" gutterBottom>
          Earnlyze
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {isLogin ? 'Войдите в личный кабинет' : 'Создайте новый аккаунт'}
        </Typography>
      </DialogTitle>

      <DialogContent>
        <Box component="form" onSubmit={handleSubmit} sx={{ mt: 1 }}>
          {!isLogin && (
            <TextField
              fullWidth
              label="Email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              margin="normal"
              required
            />
          )}
          <TextField
            fullWidth
            label={isLogin ? "Логин" : "Имя пользователя"}
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            margin="normal"
            required
          />
          <TextField
            fullWidth
            label="Пароль"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            margin="normal"
            required
          />
          {!isLogin && (
            <TextField
              fullWidth
              label="Подтвердите пароль"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              margin="normal"
              required
            />
          )}

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={{ flexDirection: 'column', gap: 2, p: 3 }}>
        <Button
          type="submit"
          fullWidth
          variant="contained"
          size="large"
          disabled={loading}
          onClick={handleSubmit}
        >
          {loading ? <CircularProgress size={24} /> : (isLogin ? 'Войти' : 'Зарегистрироваться')}
        </Button>

        <Divider sx={{ width: '100%' }} />

        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            {isLogin ? 'Нет аккаунта?' : 'Уже есть аккаунт?'}
          </Typography>
          <Link
            component="button"
            variant="body2"
            onClick={toggleMode}
            sx={{ ml: 1 }}
          >
            {isLogin ? 'Зарегистрироваться' : 'Войти'}
          </Link>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

// Страница входа (для прямых ссылок)
const LoginPage: React.FC = () => {
  return <Navigate to="/" replace />;
};

// Layout компонент
const Layout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [mobileOpen, setMobileOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { logout } = useAuth();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const handleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    logout();
    handleClose();
  };

  const drawerWidth = 240;

  const drawer = (
    <Box>
      <Toolbar>
        <Typography variant="h6" noWrap component="div">
          Earnlyze
        </Typography>
      </Toolbar>
      <List>
        <ListItem button component="a" href="/">
          <ListItemIcon>
            <DashboardIcon />
          </ListItemIcon>
          <ListItemText primary="Главная" />
        </ListItem>
        <ListItem button component="a" href="/bots">
          <ListItemIcon>
            <BotsIcon />
          </ListItemIcon>
          <ListItemText primary="Боты" />
        </ListItem>
        <ListItem button component="a" href="/exchange-keys">
          <ListItemIcon>
            <KeysIcon />
          </ListItemIcon>
          <ListItemText primary="API Ключи" />
        </ListItem>
      </List>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex' }}>
      <AppBar
        position="fixed"
        sx={{
          width: { sm: `calc(100% - ${drawerWidth}px)` },
          ml: { sm: `${drawerWidth}px` },
        }}
      >
        <Toolbar>
          <IconButton
            color="inherit"
            aria-label="open drawer"
            edge="start"
            onClick={handleDrawerToggle}
            sx={{ mr: 2, display: { sm: 'none' } }}
          >
            <MenuIcon />
          </IconButton>
          <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
            Платформа крипто ботов
          </Typography>
          <IconButton
            size="large"
            aria-label="account of current user"
            aria-controls="menu-appbar"
            aria-haspopup="true"
            onClick={handleMenu}
            color="inherit"
          >
            <AccountIcon />
          </IconButton>
          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleClose}
          >
            <MenuItem onClick={handleLogout}>
              <LogoutIcon sx={{ mr: 1 }} />
              Выйти
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>
      <Box
        component="nav"
        sx={{ width: { sm: drawerWidth }, flexShrink: { sm: 0 } }}
      >
        <Drawer
          variant="temporary"
          open={mobileOpen}
          onClose={handleDrawerToggle}
          ModalProps={{
            keepMounted: true,
          }}
          sx={{
            display: { xs: 'block', sm: 'none' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
        >
          {drawer}
        </Drawer>
        <Drawer
          variant="permanent"
          sx={{
            display: { xs: 'none', sm: 'block' },
            '& .MuiDrawer-paper': { boxSizing: 'border-box', width: drawerWidth },
          }}
          open
        >
          {drawer}
        </Drawer>
      </Box>
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          width: { sm: `calc(100% - ${drawerWidth}px)` },
        }}
      >
        <Toolbar />
        {children}
      </Box>
    </Box>
  );
};

// Публичная главная страница (лендинг)
const PublicHomePage: React.FC = () => {
  const [loginOpen, setLoginOpen] = useState(false);

  return (
    <Box sx={{ minHeight: '100vh', background: 'linear-gradient(135deg, #0F172A 0%, #1E293B 50%, #2D1B69 100%)' }}>
      {/* Навигация */}
      <AppBar position="static" sx={{ background: 'rgba(15, 23, 42, 0.9)', backdropFilter: 'blur(10px)' }}>
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1, fontWeight: 'bold' }}>
            Earnlyze
          </Typography>
          <Button color="inherit" onClick={() => setLoginOpen(true)}>
            Войти в личный кабинет
          </Button>
        </Toolbar>
      </AppBar>

      {/* Главный блок */}
      <Container maxWidth="lg" sx={{ pt: 8, pb: 8 }}>
        <Box textAlign="center" mb={8}>
          <Typography variant="h2" component="h1" gutterBottom sx={{ fontWeight: 'bold', mb: 3 }}>
            Автоматизированная торговля
            <br />
            <Box component="span" sx={{ color: 'primary.main' }}>
              нового поколения
            </Box>
          </Typography>
          <Typography variant="h5" color="text.secondary" paragraph sx={{ mb: 4, maxWidth: '800px', mx: 'auto' }}>
            Earnlyze — это профессиональная платформа для создания и управления торговыми ботами.
            Используйте сеточную торговлю для получения стабильной прибыли на криптовалютных рынках.
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              variant="contained"
              size="large"
              sx={{ px: 4, py: 1.5, fontSize: '1.1rem' }}
              onClick={() => setLoginOpen(true)}
            >
              Начать торговлю
            </Button>
            <Button
              variant="outlined"
              size="large"
              sx={{ px: 4, py: 1.5, fontSize: '1.1rem' }}
            >
              Узнать больше
            </Button>
          </Box>
        </Box>

        {/* Преимущества */}
        <Box sx={{ mt: 8 }}>
          <Typography variant="h3" component="h2" textAlign="center" gutterBottom sx={{ mb: 6 }}>
            Почему выбирают Earnlyze?
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: 'repeat(3, 1fr)' }, gap: 4 }}>
            <Paper sx={{ p: 4, textAlign: 'center', height: '100%' }}>
              <Typography variant="h4" sx={{ mb: 2 }}>🤖</Typography>
              <Typography variant="h5" gutterBottom>Умные боты</Typography>
              <Typography variant="body1" color="text.secondary">
                Продвинутые алгоритмы сеточной торговли с автоматической настройкой параметров
              </Typography>
            </Paper>
            <Paper sx={{ p: 4, textAlign: 'center', height: '100%' }}>
              <Typography variant="h4" sx={{ mb: 2 }}>🔒</Typography>
              <Typography variant="h5" gutterBottom>Безопасность</Typography>
              <Typography variant="body1" color="text.secondary">
                Ваши API ключи надежно зашифрованы. Мы никогда не получаем доступ к выводу средств
              </Typography>
            </Paper>
            <Paper sx={{ p: 4, textAlign: 'center', height: '100%' }}>
              <Typography variant="h4" sx={{ mb: 2 }}>📊</Typography>
              <Typography variant="h5" gutterBottom>Аналитика</Typography>
              <Typography variant="body1" color="text.secondary">
                Подробная статистика и отчеты по всем сделкам в режиме реального времени
              </Typography>
            </Paper>
          </Box>
        </Box>

        {/* Статистика */}
        <Box sx={{ mt: 8, textAlign: 'center' }}>
          <Typography variant="h3" component="h2" gutterBottom sx={{ mb: 6 }}>
            Наши результаты
          </Typography>
          <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', md: 'repeat(4, 1fr)' }, gap: 4 }}>
            <Box>
              <Typography variant="h3" color="primary.main" sx={{ fontWeight: 'bold' }}>
                1000+
              </Typography>
              <Typography variant="h6" color="text.secondary">
                Активных пользователей
              </Typography>
            </Box>
            <Box>
              <Typography variant="h3" color="primary.main" sx={{ fontWeight: 'bold' }}>
                $2.5M+
              </Typography>
              <Typography variant="h6" color="text.secondary">
                Объем торгов
              </Typography>
            </Box>
            <Box>
              <Typography variant="h3" color="primary.main" sx={{ fontWeight: 'bold' }}>
                15%
              </Typography>
              <Typography variant="h6" color="text.secondary">
                Средняя доходность
              </Typography>
            </Box>
            <Box>
              <Typography variant="h3" color="primary.main" sx={{ fontWeight: 'bold' }}>
                24/7
              </Typography>
              <Typography variant="h6" color="text.secondary">
                Поддержка
              </Typography>
            </Box>
          </Box>
        </Box>
      </Container>

      {/* Модальное окно входа */}
      <LoginModal open={loginOpen} onClose={() => setLoginOpen(false)} />
    </Box>
  );
};

// Главная страница для авторизованных пользователей
const DashboardPage: React.FC = () => {
  return (
    <Container maxWidth="lg">
      <Typography variant="h4" component="h1" gutterBottom>
        Добро пожаловать в Earnlyze
      </Typography>
      <Typography variant="h6" color="text.secondary" paragraph>
        Платформа крипто ботов для сеточной торговли
      </Typography>

      <Box sx={{ mt: 4 }}>
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h5" gutterBottom>
            🚀 Статистика
          </Typography>
          <Typography variant="body1">
            Активных ботов: 0
          </Typography>
          <Typography variant="body1">
            Общая прибыль: $0.00
          </Typography>
        </Paper>

        <Paper sx={{ p: 3 }}>
          <Typography variant="h5" gutterBottom>
            📊 Быстрые действия
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <Button variant="contained" href="/bots">
              Создать бота
            </Button>
            <Button variant="outlined" href="/exchange-keys">
              Добавить API ключи
            </Button>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

// Страница ботов
const BotsPage: React.FC = () => {
  return (
    <Container maxWidth="lg">
      <Typography variant="h4" component="h1" gutterBottom>
        Торговые боты
      </Typography>

      <Box sx={{ mb: 3 }}>
        <Button variant="contained" color="primary">
          Создать нового бота
        </Button>
      </Box>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Список ботов
        </Typography>
        <Typography variant="body1" color="text.secondary">
          У вас пока нет активных ботов. Создайте первого бота для начала торговли.
        </Typography>
      </Paper>
    </Container>
  );
};

// Страница API ключей
const ExchangeKeysPage: React.FC = () => {
  return (
    <Container maxWidth="lg">
      <Typography variant="h4" component="h1" gutterBottom>
        API Ключи бирж
      </Typography>

      <Box sx={{ mb: 3 }}>
        <Button variant="contained" color="primary">
          Добавить API ключ
        </Button>
      </Box>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Подключенные биржи
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Добавьте API ключи от бирж для начала торговли.
        </Typography>
      </Paper>
    </Container>
  );
};

// Основные роуты приложения
const AppRoutes: React.FC = () => {
  const { isAuthenticated, loading } = useAuth();

  if (loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: 'background.default',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Routes>
      <Route
        path="/login"
        element={<LoginPage />}
      />
      <Route
        path="/"
        element={
          isAuthenticated ? (
            <Layout>
              <DashboardPage />
            </Layout>
          ) : (
            <PublicHomePage />
          )
        }
      />
      <Route
        path="/bots"
        element={
          <ProtectedRoute>
            <Layout>
              <BotsPage />
            </Layout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/exchange-keys"
        element={
          <ProtectedRoute>
            <Layout>
              <ExchangeKeysPage />
            </Layout>
          </ProtectedRoute>
        }
      />
    </Routes>
  );
};

const App: React.FC = () => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <Router>
          <AppRoutes />
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;
