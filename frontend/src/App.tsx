import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { theme } from './theme';
import {
  HomePage,
  LoginPage,
  RegisterPage,
  BotsPage,
  ExchangeKeysPage,
  SettingsPage,
  AdminPage
} from './pages';
import { AuthProvider } from './contexts/AuthContext';
import { ProtectedRoute, Layout } from './components';

const App: React.FC = () => {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <Router>
          <Routes>
            {/* Публичные роуты */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/register" element={<RegisterPage />} />

            {/* Главная страница */}
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Layout>
                    <HomePage />
                  </Layout>
                </ProtectedRoute>
              }
            />

            {/* Защищенные роуты */}
            <Route
              path="/bots"
              element={
                <ProtectedRoute>
                  <Layout>
                    <BotsPage />
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/exchange-keys"
              element={
                <ProtectedRoute>
                  <Layout>
                    <ExchangeKeysPage />
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/settings"
              element={
                <ProtectedRoute>
                  <Layout>
                    <SettingsPage />
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/admin"
              element={
                <ProtectedRoute>
                  <Layout>
                    <AdminPage />
                  </Layout>
                </ProtectedRoute>
              }
            />

            {/* Перенаправление неизвестных роутов */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;