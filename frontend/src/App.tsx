import React, { useState, useEffect } from 'react';
import {
  Contain<PERSON>,
  Typography,
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON>,
  Grid,
  <PERSON>ert,
  CircularProgress
} from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

interface Strategy {
  id: string;
  name: string;
  symbol: string;
  status: string;
  total_profit: number;
  total_trades: number;
}

function App() {
  const [strategies, setStrategies] = useState<Strategy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchStrategies();
  }, []);

  const fetchStrategies = async () => {
    try {
      const response = await fetch('/api/v1/strategies/');
      if (response.ok) {
        const data = await response.json();
        setStrategies(data);
      } else {
        setError('Ошибка загрузки стратегий');
      }
    } catch (err) {
      setError('Ошибка подключения к серверу');
    } finally {
      setLoading(false);
    }
  };

  const createTestStrategy = async () => {
    const strategyData = {
      name: "Test Strategy " + Date.now(),
      description: "Тестовая стратегия",
      exchange: "binance",
      symbol: "BTC/USDT:USDT",
      direction: "long",
      grid_count: 5,
      grid_step_percent: 1.0,
      deposit_amount: 100.0,
      leverage: 2.0,
      tp_percent: 3.0,
      sl_percent: 5.0
    };

    try {
      const response = await fetch('/api/v1/strategies/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(strategyData),
      });

      if (response.ok) {
        fetchStrategies(); // Обновить список
      } else {
        setError('Ошибка создания стратегии');
      }
    } catch (err) {
      setError('Ошибка создания стратегии');
    }
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Container maxWidth="lg">
        <Box sx={{ my: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom align="center">
            🤖 Grid Trading Bot
          </Typography>
          
          <Typography variant="h6" align="center" color="text.secondary" paragraph>
            Бот для сеточной торговли фьючерсами
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box sx={{ mb: 3, textAlign: 'center' }}>
            <Button 
              variant="contained" 
              onClick={createTestStrategy}
              sx={{ mr: 2 }}
            >
              Создать тестовую стратегию
            </Button>
            <Button 
              variant="outlined" 
              onClick={fetchStrategies}
            >
              Обновить
            </Button>
          </Box>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Grid container spacing={3}>
              {strategies.length === 0 ? (
                <Grid item xs={12}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" align="center">
                        Стратегии не найдены
                      </Typography>
                      <Typography align="center" color="text.secondary">
                        Создайте первую стратегию для начала торговли
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ) : (
                strategies.map((strategy) => (
                  <Grid item xs={12} md={6} lg={4} key={strategy.id}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          {strategy.name}
                        </Typography>
                        <Typography color="text.secondary" gutterBottom>
                          {strategy.symbol}
                        </Typography>
                        <Box sx={{ mt: 2 }}>
                          <Typography variant="body2">
                            Статус: <strong>{strategy.status}</strong>
                          </Typography>
                          <Typography variant="body2">
                            Прибыль: <strong>{strategy.total_profit.toFixed(2)} USDT</strong>
                          </Typography>
                          <Typography variant="body2">
                            Сделок: <strong>{strategy.total_trades}</strong>
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))
              )}
            </Grid>
          )}

          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              API Документация: <a href="/api/v1/docs" target="_blank" rel="noopener noreferrer">
                /api/v1/docs
              </a>
            </Typography>
          </Box>
        </Box>
      </Container>
    </ThemeProvider>
  );
}

export default App;
