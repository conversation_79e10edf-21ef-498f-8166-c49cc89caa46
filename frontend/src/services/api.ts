import axios from 'axios';

// Создаем экземпляр axios с базовой конфигурацией
export const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Интерсептор для добавления токена авторизации
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Интерсептор для обработки ответов
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Токен недействителен, очищаем локальное хранилище
      localStorage.removeItem('auth_token');
      delete api.defaults.headers.common['Authorization'];
      
      // Перенаправляем на страницу входа
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }
    return Promise.reject(error);
  }
);

// Типы для API
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  token_type: string;
  user: {
    username: string;
    is_admin: boolean;
  };
}

export interface User {
  username: string;
  is_admin: boolean;
}

export interface ExchangeKey {
  id: number;
  name: string;
  exchange: string;
  network: string;
  is_active: boolean;
  is_validated: boolean;
  last_validation?: string;
  validation_error?: string;
  balance_updated?: string;
  created_at: string;
  updated_at?: string;
}

export interface ExchangeKeyCreate {
  name: string;
  exchange: string;
  network: string;
  api_key: string;
  secret: string;
  passphrase?: string;
}

export interface ExchangeInfo {
  id: string;
  name: string;
  has_testnet: boolean;
  requires_passphrase: boolean;
  supported_networks: string[];
}

export interface Bot {
  id: number;
  name: string;
  description?: string;
  symbol: string;
  exchange_key_id?: number;
  lower_price: number;
  upper_price: number;
  grid_count: number;
  order_amount: number;
  profit_percentage: number;
  is_active: boolean;
  total_profit: number;
  total_trades: number;
  created_at: string;
  updated_at?: string;
  started_at?: string;
  stopped_at?: string;
}

export interface BotCreate {
  name: string;
  description?: string;
  symbol: string;
  exchange_key_id: number;
  lower_price: number;
  upper_price: number;
  grid_count: number;
  order_amount: number;
  profit_percentage: number;
}

// API методы
export const authAPI = {
  login: (data: LoginRequest) => api.post<LoginResponse>('/auth/login', data),
  getMe: () => api.get<User>('/auth/me'),
  logout: () => api.post('/auth/logout'),
};

export const exchangeKeysAPI = {
  getAll: () => api.get<ExchangeKey[]>('/exchange-keys/'),
  getById: (id: number) => api.get<ExchangeKey>(`/exchange-keys/${id}`),
  create: (data: ExchangeKeyCreate) => api.post<ExchangeKey>('/exchange-keys/', data),
  update: (id: number, data: Partial<ExchangeKeyCreate>) => 
    api.put<ExchangeKey>(`/exchange-keys/${id}`, data),
  delete: (id: number) => api.delete(`/exchange-keys/${id}`),
  validate: (id: number) => api.post(`/exchange-keys/${id}/validate`),
  getExchanges: () => api.get<ExchangeInfo[]>('/exchange-keys/exchanges'),
};

export const botsAPI = {
  getAll: (params?: { skip?: number; limit?: number; active_only?: boolean }) => 
    api.get<Bot[]>('/bots/', { params }),
  getById: (id: number) => api.get<Bot>(`/bots/${id}`),
  create: (data: BotCreate) => api.post<Bot>('/bots/', data),
  update: (id: number, data: Partial<BotCreate>) => 
    api.put<Bot>(`/bots/${id}`, data),
  delete: (id: number) => api.delete(`/bots/${id}`),
  start: (id: number) => api.post(`/bots/${id}/start`),
  stop: (id: number) => api.post(`/bots/${id}/stop`),
};

export const exchangesAPI = {
  getAll: () => api.get('/exchanges/'),
  getSymbols: (exchangeName: string) => api.get(`/exchanges/${exchangeName}/symbols`),
  getMarkets: (exchangeName: string) => api.get(`/exchanges/${exchangeName}/markets`),
};
