.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-header h1 {
  margin: 0;
  font-size: 2rem;
}

main {
  padding: 20px;
}

.card {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  margin: 20px 0;
}

.button {
  background-color: #1976d2;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin: 5px;
}

.button:hover {
  background-color: #1565c0;
}

.button-outline {
  background-color: transparent;
  color: #1976d2;
  border: 1px solid #1976d2;
}

.button-outline:hover {
  background-color: #1976d2;
  color: white;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.error {
  background-color: #f44336;
  color: white;
  padding: 16px;
  border-radius: 4px;
  margin: 16px 0;
}

.success {
  background-color: #4caf50;
  color: white;
  padding: 16px;
  border-radius: 4px;
  margin: 16px 0;
}
