import { createTheme } from '@mui/material/styles';

// Темная тема в стиле Veles
export const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#7C3AED', // Фиолетовый как в Veles
      light: '#A855F7',
      dark: '#5B21B6',
      contrastText: '#FFFFFF',
    },
    secondary: {
      main: '#F59E0B', // Оранжевый для акцентов
      light: '#FCD34D',
      dark: '#D97706',
      contrastText: '#000000',
    },
    background: {
      default: '#0F172A', // Очень темный фон
      paper: '#1E293B',   // Темно-серый для карточек
    },
    surface: {
      main: '#334155',    // Средний серый для поверхностей
    },
    text: {
      primary: '#F8FAFC',   // Почти белый
      secondary: '#CBD5E1', // Светло-серый
    },
    divider: '#475569',
    success: {
      main: '#10B981',
      light: '#34D399',
      dark: '#059669',
    },
    error: {
      main: '#EF4444',
      light: '#F87171',
      dark: '#DC2626',
    },
    warning: {
      main: '#F59E0B',
      light: '#FCD34D',
      dark: '#D97706',
    },
    info: {
      main: '#3B82F6',
      light: '#60A5FA',
      dark: '#2563EB',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 600,
      color: '#F8FAFC',
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      color: '#F8FAFC',
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      color: '#F8FAFC',
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      color: '#F8FAFC',
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 600,
      color: '#F8FAFC',
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 600,
      color: '#F8FAFC',
    },
    body1: {
      fontSize: '1rem',
      color: '#CBD5E1',
    },
    body2: {
      fontSize: '0.875rem',
      color: '#94A3B8',
    },
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          backgroundColor: '#0F172A',
          color: '#F8FAFC',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#1E293B',
          borderBottom: '1px solid #475569',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#1E293B',
          borderRight: '1px solid #475569',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundColor: '#1E293B',
          border: '1px solid #475569',
          borderRadius: 12,
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
        },
        contained: {
          boxShadow: 'none',
          '&:hover': {
            boxShadow: 'none',
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            backgroundColor: '#334155',
            '& fieldset': {
              borderColor: '#475569',
            },
            '&:hover fieldset': {
              borderColor: '#64748B',
            },
            '&.Mui-focused fieldset': {
              borderColor: '#7C3AED',
            },
          },
        },
      },
    },
    MuiSelect: {
      styleOverrides: {
        root: {
          backgroundColor: '#334155',
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: '#475569',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: '#64748B',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: '#7C3AED',
          },
        },
      },
    },
    MuiTableContainer: {
      styleOverrides: {
        root: {
          backgroundColor: '#1E293B',
          border: '1px solid #475569',
          borderRadius: 12,
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: '#334155',
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:hover': {
            backgroundColor: '#334155',
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 6,
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          backgroundColor: '#1E293B',
          border: '1px solid #475569',
        },
      },
    },
  },
});

// Расширяем тему для кастомных цветов
declare module '@mui/material/styles' {
  interface Palette {
    surface: Palette['primary'];
  }

  interface PaletteOptions {
    surface?: PaletteOptions['primary'];
  }
}
